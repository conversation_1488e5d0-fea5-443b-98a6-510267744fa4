import asyncio
import sys
import os
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.state_manager.variable_condition_parser import VariableConditionParser
from core.memory.memory_manager import MemoryManager
from core.session_manager.session_manager_v2 import SessionManagerV2
from agents.orchestrator_agent_v3 import OrchestratorV3

async def test_variable_extraction():
    """Test variable extraction from conditions"""
    print("=== Testing Variable Extraction ===")
    
    parser = VariableConditionParser("test_session")
    
    test_conditions = [
        "amount >= 60000",
        "balance < amount",
        "account_type == 'premium'",
        "amount >= 60000 and balance > 1000",
        "intent == 'transfer' and amount < balance",
        "true",
        "false"
    ]
    
    for condition in test_conditions:
        variables = parser.extract_variables_from_condition(condition)
        print(f"Condition: '{condition}' -> Variables: {variables}")
    
    print()

async def test_context_building():
    """Test building evaluation context from memory"""
    print("=== Testing Context Building ===")
    
    # Create memory manager
    memory_manager = MemoryManager("test_session", "test_user")
    
    # Set some test variables in memory
    await memory_manager.set("contextual", "test_session_TransferFunds_amount", 75000)
    await memory_manager.set("contextual", "test_session_TransferFunds_balance", 100000)
    await memory_manager.set("contextual", "test_session_TransferFunds_account_type", "premium")
    await memory_manager.set("contextual", "intent", "fund_transfer")
    
    parser = VariableConditionParser("test_session")
    variables = {"amount", "balance", "account_type", "intent"}
    
    context = await parser.build_evaluation_context(
        variables, 
        memory_manager, 
        "TransferFunds"
    )
    
    print(f"Built context: {context}")
    print()

async def test_condition_evaluation():
    """Test condition evaluation with real context"""
    print("=== Testing Condition Evaluation ===")
    
    # Create memory manager and set test data
    memory_manager = MemoryManager("test_session", "test_user")
    await memory_manager.set("contextual", "test_session_TransferFunds_amount", 75000)
    await memory_manager.set("contextual", "test_session_TransferFunds_balance", 100000)
    await memory_manager.set("contextual", "intent", "fund_transfer")
    
    parser = VariableConditionParser("test_session")
    
    test_conditions = [
        ("amount >= 60000", True),  # Should be True (75000 >= 60000)
        ("balance < amount", False),  # Should be False (100000 < 75000)
        ("amount < balance", True),   # Should be True (75000 < 100000)
        ("intent == 'fund_transfer'", True),  # Should be True
    ]
    
    for condition, expected in test_conditions:
        variables = parser.extract_variables_from_condition(condition)
        context = await parser.build_evaluation_context(
            variables, 
            memory_manager, 
            "TransferFunds"
        )
        
        # Evaluate condition
        from asteval import Interpreter
        aeval = Interpreter()
        for key, value in context.items():
            aeval.symtable[key] = value
        
        try:
            result = aeval(condition)
            status = "✓" if result == expected else "✗"
            print(f"{status} Condition: '{condition}' -> {result} (expected: {expected})")
        except Exception as e:
            print(f"✗ Condition: '{condition}' -> Error: {e}")
    
    print()

async def test_workflow_integration():
    """Test integration with workflow transitions"""
    print("=== Testing Workflow Integration ===")
    
    try:
        # Initialize session manager
        session_manager = SessionManagerV2()
        await session_manager.initialize()
        
        # Create session with banking workflow
        session_id = await session_manager.create_session("banking_workflow_v2")
        print(f"Created session: {session_id}")
        
        # Get memory manager and set test data for high-value transfer
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_amount", 75000)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_balance", 100000)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_currency", "USD")
        await memory_manager.set("contextual", "intent", "fund_transfer")
        await memory_manager.set("contextual", "clean_text", "I want to transfer $75,000")
        
        # Get orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)
        
        # Test enhanced workflow transition evaluation
        next_state = await orchestrator.evaluate_workflow_transitions_with_variables(
            "I want to transfer $75,000",
            "fund_transfer", 
            "TransferFunds"
        )
        
        print(f"Next workflow state: {next_state}")
        
        # Test with insufficient funds scenario
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_amount", 150000)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_balance", 100000)
        
        next_state_insufficient = await orchestrator.evaluate_workflow_transitions_with_variables(
            "I want to transfer $150,000",
            "fund_transfer", 
            "TransferFunds"
        )
        
        print(f"Next workflow state (insufficient funds): {next_state_insufficient}")
        
        # Cleanup
        await session_manager.cleanup_session(session_id)
        await session_manager.shutdown()
        
    except Exception as e:
        print(f"Error in workflow integration test: {e}")
        import traceback
        traceback.print_exc()
    
    print()

async def main():
    """Run all tests"""
    print("Starting Variable-Based Conditional Transitions Tests\n")
    
    await test_variable_extraction()
    await test_context_building()
    await test_condition_evaluation()
    await test_workflow_integration()
    
    print("All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
