#!/usr/bin/env python3
"""
Test Script for Action Reversibility System
Tests the undo functionality in the AI Voice Agents Platform
"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.logging.logger_config import get_module_logger

logger = get_module_logger("reversibility_test")


def test_workflow_has_anti_states():
    """Test that the workflow JSON contains anti-states"""
    print("🧪 Test 1: Checking workflow anti-states...")

    try:
        # Load the workflow JSON directly
        workflow_path = Path(project_root) / "workflows" / "banking_workflow_v2.json"

        if not workflow_path.exists():
            print(f"❌ Workflow file not found: {workflow_path}")
            return False

        with open(workflow_path, 'r') as f:
            workflow_data = json.load(f)

        # The workflow JSON has a nested structure
        states = workflow_data.get("workflow", {}).get("states", {})

        # Check if TransferFunds has anti_state
        transfer_funds = states.get("TransferFunds")
        if transfer_funds:
            print(f"🔍 TransferFunds found with keys: {list(transfer_funds.keys())}")
            if transfer_funds.get("anti_state"):
                print(f"✅ TransferFunds has anti_state: {transfer_funds['anti_state']}")
                if transfer_funds["anti_state"] != "ReverseTransferFunds":
                    print(f"❌ Expected 'ReverseTransferFunds', got '{transfer_funds['anti_state']}'")
                    return False
            else:
                print("❌ TransferFunds missing anti_state")
                print(f"🔍 TransferFunds content: {json.dumps(transfer_funds, indent=2)}")
                return False
        else:
            print("❌ TransferFunds state not found")
            print(f"🔍 Available states: {list(states.keys())}")
            return False

        # Check if ReverseTransferFunds exists
        reverse_transfer = states.get("ReverseTransferFunds")
        if reverse_transfer:
            print(f"✅ ReverseTransferFunds state exists")
        else:
            print("❌ ReverseTransferFunds state missing")
            return False

        # Check if ConfirmReverse exists
        confirm_reverse = states.get("ConfirmReverse")
        if confirm_reverse:
            print(f"✅ ConfirmReverse state exists")
        else:
            print("❌ ConfirmReverse state missing")
            return False

        # Check if RepeatedUserQuery has undo transition to last_anti_state
        repeated_query = states.get("RepeatedUserQuery")
        if repeated_query:
            transitions = repeated_query.get("transitions", [])
            undo_transition = None
            for transition in transitions:
                if "undo" in transition.get("condition", ""):
                    undo_transition = transition
                    break

            if undo_transition and undo_transition.get("target") == "last_anti_state":
                print("✅ RepeatedUserQuery has undo → last_anti_state transition")
            else:
                print("❌ RepeatedUserQuery missing undo → last_anti_state transition")
                return False
        else:
            print("❌ RepeatedUserQuery state missing")
            return False

        # Check if ReverseTransferFunds has confirm/cancel transitions
        reverse_transitions = reverse_transfer.get("transitions", [])
        has_confirm = any("confirm" in t.get("condition", "") for t in reverse_transitions)
        has_cancel = any("cancel" in t.get("condition", "") for t in reverse_transitions)

        if has_confirm and has_cancel:
            print("✅ ReverseTransferFunds has confirm/cancel transitions")
        else:
            print("❌ ReverseTransferFunds missing confirm/cancel transitions")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_anti_state_layer2_exists():
    """Test that the anti-state Layer2 pipeline exists"""
    print("🧪 Test 2: Checking anti-state Layer2 pipeline...")

    try:
        # Check if the reverse transfer Layer2 file exists
        layer2_path = Path(project_root) / "workflows" / "l2_reverse_transfer_funds.json"

        if not layer2_path.exists():
            print(f"❌ Layer2 file not found: {layer2_path}")
            return False

        with open(layer2_path, 'r') as f:
            layer2_data = json.load(f)

        # Check basic structure
        if layer2_data["id"] != "l2_reverse_transfer_funds":
            print(f"❌ Expected ID 'l2_reverse_transfer_funds', got '{layer2_data['id']}'")
            return False

        if "pipeline" not in layer2_data:
            print("❌ No pipeline found in Layer2 data")
            return False

        if not isinstance(layer2_data["pipeline"], list) or len(layer2_data["pipeline"]) == 0:
            print("❌ Pipeline is not a valid list or is empty")
            return False

        print(f"✅ Layer2 pipeline exists with {len(layer2_data['pipeline'])} steps")

        # Check for processing step (now uses standard processing_process)
        processing_steps = [step for step in layer2_data["pipeline"] if step.get("step") == "processing"]
        if processing_steps:
            processing_step = processing_steps[0]
            if processing_step.get("process") == "processing_process":
                print("✅ Processing step found (uses standard processing_process)")
            else:
                print(f"⚠️ Processing step found but unexpected process: {processing_step.get('process')}")
        else:
            print("❌ No processing step found in pipeline")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_preprocessing_agent_has_new_intents():
    """Test that preprocessing agent recognizes confirm and cancel intents"""
    print("🧪 Test 2.3: Checking preprocessing agent intent recognition...")

    try:
        # Check if the preprocessing agent file contains the new intents
        preprocessing_path = Path(project_root) / "agents" / "processing" / "preprocessing_agent.py"

        if not preprocessing_path.exists():
            print(f"❌ Preprocessing agent file not found: {preprocessing_path}")
            return False

        with open(preprocessing_path, 'r') as f:
            preprocessing_content = f.read()

        # Check for confirm and cancel intents in the prompt
        if '- confirm:' in preprocessing_content and '- cancel:' in preprocessing_content:
            print("✅ Preprocessing agent has confirm and cancel intents")
        else:
            print("❌ Preprocessing agent missing confirm/cancel intents")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_confirm_reverse_layer2_exists():
    """Test that the confirmReverse Layer2 pipeline exists"""
    print("🧪 Test 2.5: Checking confirmReverse Layer2 pipeline...")

    try:
        # Check if the confirm reverse Layer2 file exists
        layer2_path = Path(project_root) / "workflows" / "l2_confirm_reverse.json"

        if not layer2_path.exists():
            print(f"❌ Layer2 file not found: {layer2_path}")
            return False

        with open(layer2_path, 'r') as f:
            layer2_data = json.load(f)

        # Check basic structure
        if layer2_data["id"] != "l2_confirm_reverse":
            print(f"❌ Expected ID 'l2_confirm_reverse', got '{layer2_data['id']}'")
            return False

        if "pipeline" not in layer2_data:
            print("❌ No pipeline found in Layer2 data")
            return False

        if not isinstance(layer2_data["pipeline"], list) or len(layer2_data["pipeline"]) == 0:
            print("❌ Pipeline is not a valid list or is empty")
            return False

        print(f"✅ confirmReverse Layer2 pipeline exists with {len(layer2_data['pipeline'])} steps")

        # Check for processing step
        processing_steps = [step for step in layer2_data["pipeline"] if step.get("step") == "processing"]
        if processing_steps:
            processing_step = processing_steps[0]
            if processing_step.get("process") == "processing_process":
                print("✅ Processing step found (uses standard processing_process)")
            else:
                print(f"⚠️ Processing step found but unexpected process: {processing_step.get('process')}")
        else:
            print("❌ No processing step found in pipeline")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_state_manager_has_reversibility_methods():
    """Test that StateManager has the reversibility methods"""
    print("🧪 Test 3: Checking StateManager reversibility methods...")

    try:
        # Import StateManager and check if it has the required methods
        from core.state_manager.state_manager import StateManager

        if hasattr(StateManager, '_resolve_anti_state'):
            print("✅ StateManager has _resolve_anti_state method")
        else:
            print("❌ StateManager missing _resolve_anti_state method")
            return False

        if hasattr(StateManager, '_track_reversible_action'):
            print("✅ StateManager has _track_reversible_action method")
        else:
            print("❌ StateManager missing _track_reversible_action method")
            return False

        return True

    except ImportError as e:
        print(f"❌ Failed to import StateManager: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def test_orchestrator_has_intent_support():
    """Test that OrchestratorV3 supports confirm and cancel intents"""
    print("🧪 Test 4: Checking Orchestrator intent support...")

    try:
        # Check if the orchestrator file contains confirm/cancel intent support
        orchestrator_path = Path(project_root) / "agents" / "orchestrator_agent_v3.py"

        if not orchestrator_path.exists():
            print(f"❌ Orchestrator file not found: {orchestrator_path}")
            return False

        with open(orchestrator_path, 'r') as f:
            orchestrator_content = f.read()

        # Check for confirm and cancel intent support in LLM prompts
        if 'intent is "confirm"' in orchestrator_content and 'intent is "cancel"' in orchestrator_content:
            print("✅ Orchestrator has confirm/cancel intent support")
        else:
            print("❌ Orchestrator missing confirm/cancel intent support")
            return False

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


def run_all_tests():
    """Run all reversibility tests"""
    print("🚀 Starting Reversibility System Tests")
    print("=" * 60)

    tests = [
        ("Workflow Anti-States", test_workflow_has_anti_states),
        ("Anti-State Layer2 Pipeline", test_anti_state_layer2_exists),
        ("Preprocessing Agent Intents", test_preprocessing_agent_has_new_intents),
        ("Confirm Reverse Layer2 Pipeline", test_confirm_reverse_layer2_exists),
        ("StateManager Reversibility Methods", test_state_manager_has_reversibility_methods),
        ("Orchestrator Intent Support", test_orchestrator_has_intent_support)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Reversibility system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


def main():
    """Main test function"""
    try:
        success = run_all_tests()

        if success:
            print("\n✅ Reversibility system test completed successfully!")
            return 0
        else:
            print("\n❌ Reversibility system test failed!")
            return 1

    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        return 1


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
