#!/usr/bin/env python3
"""
Comprehensive test for RepeatedUserQuery functionality after removing RepeatedUserQueryState class.

This test validates:
1. Context clearing (intent keys are removed from memory)
2. Engagement message is set in memory
3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> can read the engagement message
4. Complete pipeline flow works correctly
5. Edge cases (missing memory manager, etc.)

Usage:
    python test_repeated_user_query_comprehensive.py
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.state_manager.state import State
from core.memory.memory_manager import MemoryManager
from core.logging.logger_config import get_module_logger
from schemas.workflow_schema import Layer2, PipelineStep, Tools

logger = get_module_logger("repeated_user_query_comprehensive_test")

async def test_context_clearing():
    """Test that RepeatedUserQuery state properly clears intent context."""
    print("🧹 Test 1: Context Clearing")
    print("-" * 40)
    
    session_id = "test_context_clearing"
    memory_manager = MemoryManager(session_id)
    
    # Set up some intent keys that should be cleared
    intent_keys = [
        "intent", "intent_confidence", "user_query", "processed_intent",
        "previous_action", "cached_intent", "intent_history", "clean_text",
        "emotion", "gender", "preprocessing_confidence_value",
        "last_user_message", "last_user_intent", "transcript"
    ]
    
    # Pre-populate memory with intent data
    for key in intent_keys:
        await memory_manager.set("contextual", key, f"old_value_{key}")
    
    # Verify data is set
    stored_values = {}
    for key in intent_keys:
        stored_values[key] = await memory_manager.get(key)
    
    print(f"   ✅ Pre-populated {len([v for v in stored_values.values() if v])} intent keys")
    
    # Create State with RepeatedUserQuery ID
    layer2_config = Layer2(
        id="l2_repeated_user_query_simple",
        version="2.0",
        pipeline=[]  # Empty for this test
    )
    
    state = State(
        state_id="RepeatedUserQuery",
        config={},
        layer2_config=layer2_config,
        memory=memory_manager,
        tools_registry=None
    )
    
    # Call the _handle_repeated_user_query function directly
    await state._handle_repeated_user_query()
    
    # Verify keys are cleared
    cleared_count = 0
    for key in intent_keys:
        value = await memory_manager.get(key)
        if value is None:
            cleared_count += 1
    
    print(f"   ✅ Cleared {cleared_count}/{len(intent_keys)} intent keys")
    
    # Verify engagement message is set
    engagement_message = await memory_manager.get("text")
    is_engagement = await memory_manager.get("is_engagement_question")
    
    print(f"   ✅ Engagement message set: '{engagement_message}'")
    print(f"   ✅ Engagement flag set: {is_engagement}")
    
    assert engagement_message == "Is there anything else I can help you with today?"
    assert is_engagement is True
    assert cleared_count == len(intent_keys)
    
    print("   🎉 Context clearing test PASSED!")
    return True

async def test_memory_integration():
    """Test that Layer2Pipeline can read the engagement message from memory."""
    print("\n💾 Test 2: Memory Integration")
    print("-" * 40)
    
    session_id = "test_memory_integration"
    memory_manager = MemoryManager(session_id)
    
    # Simulate the _handle_repeated_user_query function setting the message
    engagement_message = "Is there anything else I can help you with today?"
    await memory_manager.set("contextual", "text", engagement_message)
    await memory_manager.set("contextual", "engagement_message", engagement_message)
    await memory_manager.set("contextual", "is_engagement_question", True)
    
    print(f"   ✅ Set engagement message in memory: '{engagement_message}'")
    
    # Simulate Layer2Pipeline reading from memory (like it does in layer2_pipeline.py)
    step_input = {}
    input_mapping = {"text": "text", "emotion": "neutral", "gender": "female"}
    
    for input_key, input_ref in input_mapping.items():
        if input_ref == "text":  # This is the key one we care about
            memory_value = await memory_manager.get(input_ref)
            if memory_value:
                step_input[input_key] = memory_value
        else:
            step_input[input_key] = input_ref  # Default values
    
    print(f"   ✅ Layer2Pipeline would receive: {step_input}")
    
    # Verify the TTS agent would get the correct text
    assert step_input.get("text") == engagement_message
    assert "text" in step_input
    
    print("   🎉 Memory integration test PASSED!")
    return True

async def test_state_execution_flow():
    """Test the complete State.execute() flow for RepeatedUserQuery."""
    print("\n🔄 Test 3: State Execution Flow")
    print("-" * 40)
    
    session_id = "test_state_execution"
    memory_manager = MemoryManager(session_id)
    
    # Create a minimal Layer2 config for testing
    layer2_config = Layer2(
        id="l2_repeated_user_query_simple",
        version="2.0",
        pipeline=[
            PipelineStep(
                step="engagement_tts",
                process="tts_process",
                agent="tts_agent",
                input={"text": "text", "emotion": "neutral", "gender": "female"},
                tools=Tools(external_tools="openai"),
                output={"audio_path": "engagement_audio_path", "latencyTTS": "engagement_latencyTTS"}
            )
        ]
    )
    
    # Create State with RepeatedUserQuery ID
    state = State(
        state_id="RepeatedUserQuery",
        config={},
        layer2_config=layer2_config,
        memory=memory_manager,
        tools_registry=None  # We'll mock this
    )
    
    print("   ✅ Created RepeatedUserQuery state")
    
    # Test the state ID detection
    assert state.id == "RepeatedUserQuery"
    print("   ✅ State ID correctly set to 'RepeatedUserQuery'")
    
    # Test the _handle_repeated_user_query function
    await state._handle_repeated_user_query()
    
    # Verify the function worked
    engagement_message = await memory_manager.get("text")
    context_reset = await memory_manager.get("context_reset")
    
    print(f"   ✅ Function executed, message set: '{engagement_message}'")
    print(f"   ✅ Context reset flag: {context_reset}")
    
    assert engagement_message == "Is there anything else I can help you with today?"
    assert context_reset is True
    
    print("   🎉 State execution flow test PASSED!")
    return True

async def test_edge_cases():
    """Test edge cases and error handling."""
    print("\n⚠️  Test 4: Edge Cases")
    print("-" * 40)
    
    # Test 1: Memory manager with no existing keys
    session_id = "test_edge_cases"
    memory_manager = MemoryManager(session_id)
    
    layer2_config = Layer2(
        id="l2_repeated_user_query_simple",
        version="2.0",
        pipeline=[]
    )
    
    state = State(
        state_id="RepeatedUserQuery",
        config={},
        layer2_config=layer2_config,
        memory=memory_manager,
        tools_registry=None
    )
    
    # Should not fail even with empty memory
    await state._handle_repeated_user_query()
    
    engagement_message = await memory_manager.get("text")
    print(f"   ✅ Works with empty memory: '{engagement_message}'")
    
    # Test 2: Multiple calls (should work repeatedly)
    await state._handle_repeated_user_query()
    await state._handle_repeated_user_query()
    
    final_message = await memory_manager.get("text")
    print(f"   ✅ Works with multiple calls: '{final_message}'")
    
    assert engagement_message == final_message
    
    print("   🎉 Edge cases test PASSED!")
    return True

async def test_workflow_configuration():
    """Test that the workflow configuration is correct."""
    print("\n📋 Test 5: Workflow Configuration")
    print("-" * 40)
    
    # Load and verify the actual workflow configuration
    import json
    
    try:
        with open("workflows/l2_repeated_user_query_simple.json", "r") as f:
            workflow_config = json.load(f)
        
        print("   ✅ Workflow configuration loaded successfully")
        
        # Verify key structure
        assert "pipeline" in workflow_config
        assert len(workflow_config["pipeline"]) > 0
        
        # Find the engagement_tts step
        engagement_step = None
        for step in workflow_config["pipeline"]:
            if step["step"] == "engagement_tts":
                engagement_step = step
                break
        
        assert engagement_step is not None
        print("   ✅ Found engagement_tts step")
        
        # Verify it reads from "text" in memory
        assert engagement_step["input"]["text"] == "text"
        print("   ✅ engagement_tts step correctly configured to read 'text' from memory")
        
        # Verify the pipeline flow
        expected_steps = ["engagement_tts", "stt", "preprocessing", "processing", "response_tts"]
        actual_steps = [step["step"] for step in workflow_config["pipeline"]]
        
        print(f"   ✅ Pipeline steps: {actual_steps}")
        assert actual_steps == expected_steps
        
        print("   🎉 Workflow configuration test PASSED!")
        return True
        
    except FileNotFoundError:
        print("   ❌ Workflow configuration file not found")
        return False
    except Exception as e:
        print(f"   ❌ Workflow configuration test failed: {e}")
        return False

async def main():
    """Run all comprehensive tests."""
    print("🎯 RepeatedUserQuery Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        test_context_clearing,
        test_memory_integration,
        test_state_execution_flow,
        test_edge_cases,
        test_workflow_configuration
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   Test {i+1}: {test.__name__} - {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! RepeatedUserQuery functionality is working correctly.")
        print("\n💡 Key Findings:")
        print("   ✅ Context clearing works properly")
        print("   ✅ Engagement message is set correctly")
        print("   ✅ Layer2Pipeline integration is functional")
        print("   ✅ Workflow configuration is correct")
        print("   ✅ Edge cases are handled")
        print("\n🚀 The simple function approach successfully replaces the complex class!")
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())
