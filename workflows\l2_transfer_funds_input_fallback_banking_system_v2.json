{"id": "l2_transfer_funds_input_fallback_banking_system_v2", "version": "1.0", "pipeline": [{"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}, "expected_output": ["account_id", "amount", "currency"]}, {"step": "stt", "process": "stt_process", "agent": "stt_agent", "input": {"audio_path": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "agent": "stt_agent", "input": {"transcript": "text"}, "tools": {"external_tools": "openai"}, "output": {"fallback_message": "fallback_message", "emotion": "emotion", "intent": "intent", "gender": "gender", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "processing", "process": "processing_process", "agent": "stt_agent", "input": {"clean_text": "clean_text", "intent": "intent"}, "tools": {"external_tools": "openai"}, "output": {"llm_answer": "llm_answer", "latencyProcessing": "latencyProcessing"}, "expected_output": ["account_id", "amount", "currency"]}, {"step": "tts", "process": "tts_process", "agent": "tts_agent", "input": {"text": "llm_answer", "emotion": "emotion", "gender": "gender"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "backChanneling": true, "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}