import asyncio
import os
import json
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import aiofiles  # Add this import at the top

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.audio.audio_player import AudioPlayer
from core.logging.exceptions import ConfigurationError, WorkflowError, Layer2Error
from core.logging.logger import Logger, StateOutput as LoggerStateOutput
from core.logging.logger_config import get_module_logger
from core.orchestrator.agent_registry import AgentRegistry, REGISTRY_REDIS_KEY
from core.memory.redis_context import RedisClient
from agents.filler.filler_tts_agent import FillerTTSAgent
from agents.processing.preprocessing_agent import PreprocessingAgent
from agents.processing.processing_agent import ProcessingAgent
from agents.tts.tts_agent import TTSAgent
from agents.stt.stt_agent import STTAgent
from agents.tts.tts_openai import TTSAgentOpenAI
from schemas.agent_metadata import AgentMetadata

from core.state_manager.loaders.WorkflowSchemaLoader import WorkflowSchemaLoader
from core.state_manager.loaders.Layer2SchemaLoader import Layer2SchemaLoader
from schemas.workflow_schema import WorkflowWrapper, State, Layer2, PipelineStep
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from asteval import Interpreter
from core.memory.memory_manager import MemoryManager
from core.state_manager.state_output import (
    AbstractPipelineState, STTState, PreProcessingState, ProcessingState, FillerState, TTSState
)
from core.config.interrupt_config import get_interrupt_config
from core.interruption.interrupt_integration import InterruptIntegration

logger = get_module_logger("state_manager")

# Add config loader
async def load_config():
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_path = os.path.join(base_dir, "workflows", "state_manager_config.json")
    try:
        async with aiofiles.open(config_path, 'r') as f:
            content = await f.read()
            return json.loads(content)
    except Exception as e:
        logger.error(
            "Could not load config file",
            action="load_config",
            input_data={"config_path": config_path},
            reason=str(e),
            layer="configuration"
        )
        raise ConfigurationError(f"Failed to load state manager configuration: {e}")

class StateManager:
    """
    Main State Manager - the brain of the agent's execution loop.
    
    This class manages:
    - Workflow state transitions based on the provided schema format
    - Execution of Layer2 pipelines
    - Memory management 
    - Agent coordination (TODO: integrate with MCPAgentRegistry)
    """
    
    def __init__(self, workflow_name: str, session_id: str, user_id: Optional[str] = None, _internal_init: bool = False, audioPlayer: AudioPlayer=None):
        """
        Initialize the State Manager with basic attributes.
        Note: This constructor only sets up basic attributes. Use create() for full async initialization.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
            _internal_init: Internal flag to prevent direct instantiation
        
        Raises:
            RuntimeError: If initialized directly instead of through create()
        """
        if not _internal_init:
            raise RuntimeError(
                "StateManager cannot be initialized directly. Use StateManager.create() instead."
            )
            
        self.workflow_name = workflow_name
        self.user_id = user_id
        self.session_id = session_id
        self.logger = get_module_logger("state_manager")
        self.base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        # Initialize attributes that will be set during async initialization
        self.config = None
        self.workflow_file_path = None
        self.workflow = None  # TODO: WorkflowWrapper type
        self.layer2_map = {}  # TODO: Dict[str, Layer2] type
        self.memory_manager = None
        self.execution_history = []
        self.is_running = True
        self.current_workflow_state_id = None
        self.current_pipeline_step_id = None
        self.redis_client = RedisClient()
        self.agent_registry = AgentRegistry(self.redis_client)
        self.pipeline_state_map = {}
        self.audioPlayer = audioPlayer
        self.indexOfCurrentPipelineStep = 0

        # Reversibility system - using memory-based list for independent actions


    @classmethod
    async def create(cls, workflow_name: str, session_id: str, user_id: Optional[str] = None, audioPlayer: AudioPlayer=None):
        """
        Async factory method to create and fully initialize a StateManager instance.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
            
        Returns:
            StateManager: Fully initialized StateManager instance
            
        Raises:
            ConfigurationError: If configuration files cannot be loaded
            WorkflowError: If the workflow cannot be loaded or is invalid
            Layer2Error: If a Layer2 definition cannot be loaded or is invalid
        """
        # Pass internal flag to allow initialization
        instance = cls(workflow_name, session_id, user_id, _internal_init=True, audioPlayer=audioPlayer)
        await instance._async_initialize()
        return instance

    async def _async_initialize(self):
        """
        Perform async initialization of the StateManager.
        """
        logger.info(
            "Initializing StateManager",
            action="initialize",
            input_data={
                "workflow": self.workflow_name,
                "session_id": self.session_id,
                "user_id": self.user_id
            },
            layer="state_management"
        )
        
        # Load configuration
        try:
            self.config = await load_config()
            workflow_states_dir = self.config["paths"]["workflow_states_dir"]
            self.workflow_file_path = os.path.join(self.base_dir, workflow_states_dir, self.workflow_name)
        except ConfigurationError as e:
            logger.error(
                "Configuration error during StateManager initialization",
                action="initialize",
                reason=str(e),
                layer="state_management"
            )
            raise

        # Initialize memory manager
        self.memory_manager = MemoryManager(self.session_id, self.user_id)

        # Initialize agent registry
        await self.agent_registry.initialize()
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="stt_agent", version="1.0", description="Speech-to-Text Agent"), agentObject=STTAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="tts_agent", version="1.0", description="Text-to-Speech Agent"), agentObject=TTSAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="filler_tts_agent", version="1.0", description="Filler Text-to-Speech Agent"), agentObject=FillerTTSAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="preprocessing_agent", version="1.0", description="Preprocessing Agent"), agentObject=PreprocessingAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="processing_agent", version="1.0", description="Processing Agent"), agentObject=ProcessingAgent(self.session_id, self.current_workflow_state_id))
        await self.agent_registry.register(agent_meta=AgentMetadata(agent_name="tts_agent_openAI", version="1.0", description="Text-to-Speech Agent OpenAI"), agentObject=TTSAgentOpenAI(self.session_id, self.current_workflow_state_id))

        self.pipeline_state_map = {
            "stt_process": STTState,
            "preprocessing_process": PreProcessingState,
            "processing_process": ProcessingState,
            "filler_tts_process": FillerState,
            "tts_process": TTSState,
        }

        # Update logger context with session information
        logger.update_context(session_id=self.session_id, state_id="initialization")

        try:
            await self._load_workflow_async()

            # Load interrupt configuration with workflow overrides
            self.interrupt_config = get_interrupt_config(self.raw_workflow_dict)
            # Initialize simple interrupt integration
            self.interrupt_integration = InterruptIntegration(
                session_id=self.session_id,
                memory_manager=self.memory_manager,
                interrupt_config=self.interrupt_config
            )


            await self.check_and_persist_workflow_summary()
            await self._load_all_layer2_async()
            pass

            # Set initial state
            if self.workflow and hasattr(self.workflow.workflow, 'start'):
                self.current_workflow_state_id = self.workflow.workflow.start
                logger.update_context(state_id=self.current_workflow_state_id)
                logger.info(
                    "StateManager initialization completed",
                    action="initialize",
                    output_data={"initial_state": self.current_workflow_state_id},
                    layer="state_management"
                )
                
                # Set initial Layer2 step
                if self.current_workflow_state_id:
                    current_state = self.get_state(self.current_workflow_state_id)
                    if current_state and hasattr(current_state, 'layer2_id'):
                        layer2 = self.layer2_map.get(current_state.layer2_id)
                        if layer2 and layer2.pipeline:
                            self.current_pipeline_step_id = layer2.pipeline[0].step
        except Exception as e:
            logger.error(
                "Error initializing StateManager",
                action="initialize",
                reason=str(e),
                layer="state_management"
            )
            self.workflow = None
            raise

    async def _load_workflow_async(self):
        """
        Asynchronously load the workflow schema.
        """
        try:
            print("Loading workflow from:", self.workflow_file_path)

            # Load the raw workflow dictionary for interrupt configuration
            import aiofiles
            import json
            async with aiofiles.open(self.workflow_file_path, 'r') as f:
                self.raw_workflow_dict = json.loads(await f.read())

            # Load the structured workflow object
            self.workflow = await WorkflowSchemaLoader.load(self.workflow_file_path)
            if not self.workflow:
                raise Exception(f"Failed to load workflow: {self.workflow_file_path}")
        except Exception as e:
            logger.error(
                "Error loading workflow",
                action="_load_workflow_async",
                input_data={"workflow_file_path": self.workflow_file_path},
                reason=str(e),
                layer="state_management"
            )
            raise

    async def _initialize_session_metadata(self):
        """
        Initialize session metadata in contextual memory for tracking throughout the session.
        """
        from datetime import datetime
        if self.memory_manager and self.workflow:
            # Store workflow and session information
            await self.memory_manager.set("contextual", "workflow_name", self.workflow_name)
            await self.memory_manager.set("contextual", "pipeline_id", self.workflow.workflow.id)
            await self.memory_manager.set("contextual", "current_state", self.current_state_id)
            await self.memory_manager.set("contextual", "states_visited", [self.current_state_id])
            await self.memory_manager.set("contextual", "interrupts", [])
            await self.memory_manager.set("contextual", "session_start_time", datetime.now().isoformat())
            await self.memory_manager.set("contextual", "outcome", "in_progress")

            # Store complete workflow metadata for pipeline saving
            workflow_states = []
            if hasattr(self.workflow.workflow, 'states') and self.workflow.workflow.states:
                workflow_states = list(self.workflow.workflow.states.keys())

            await self.memory_manager.set("contextual", "workflow_states", workflow_states)
            await self.memory_manager.set("contextual", "workflow_description", getattr(self.workflow.workflow, 'description', f"Workflow: {self.workflow_name}"))
            await self.memory_manager.set("contextual", "workflow_version", getattr(self.workflow.workflow, 'version', "1.0"))

            logger.info(
                "Initialized session metadata in contextual memory",
                action="_initialize_session_metadata",
                output_data={
                    "workflow_name": self.workflow_name,
                    "pipeline_id": self.workflow.workflow.id,
                    "initial_state": self.current_state_id
                },
                layer="state_management"
            )

    async def _load_all_layer2_async(self):
        # Create a semaphore to limit concurrent loads
        semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent loads
        
        layer2_files_to_load = []
        for layer2_id, state in self.workflow.workflow.states.items():
            if not state.layer2_id:
                continue
            layer2_file = f"{self.config['paths']['layer2_config_dir']}/{state.layer2_id}.json"
            layer2_files_to_load.append((state.layer2_id, layer2_file, state.expected_output))

        # Create tasks with semaphore control
        async def load_with_semaphore(layer2_id, layer2_file, expected_output):
            async with semaphore:
                try:
                    return layer2_id, await Layer2SchemaLoader.load(layer2_file, expected_output)
                except Exception as e:
                    logger.error(
                        "Error loading Layer2",
                        action="_load_all_layer2_async",
                        input_data={"layer2_id": layer2_id, "layer2_file": layer2_file},
                        reason=str(e),
                        layer="state_management"
                    )
                    return layer2_id, None

        # Create all tasks
        tasks = [load_with_semaphore(l2_id, l2_file, exp_output) 
                 for l2_id, l2_file, exp_output in layer2_files_to_load]
        
        # Wait for all tasks to complete, even if some fail
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                logger.error(
                    "Task failed during Layer2 loading",
                    action="_load_all_layer2_async",
                    reason=str(result),
                    layer="state_management"
                )
                continue
                
            layer2_id, layer2_obj = result
            if layer2_obj:
                self.layer2_map[layer2_id] = layer2_obj
            else:
                logger.warning(
                    "Failed to load Layer2",
                    action="_load_all_layer2_async",
                    input_data={"layer2_id": layer2_id},
                    layer="state_management"
                )

    def get_state(self, state_id: str):
        return self.workflow.workflow.states.get(state_id)

    def get_layer2(self, layer2_id: str):
        return self.layer2_map.get(layer2_id)

    async def execute_step(self, input_data: dict) -> StateOutput:
        """
        Executes a single step of the workflow using the current state.
        """
        start_time = time.perf_counter()

        # Update logger context for current state
        logger.update_context(session_id=self.session_id, state_id=self.current_workflow_state_id)

        logger.info(
            "Starting state execution",
            action="execute_step",
            input_data={"current_state": self.current_workflow_state_id, "input": input_data},
            layer="state_management"
        )

        try :
            state_config = self.get_state(self.current_workflow_state_id)
            layer2_config = self.get_layer2(state_config.layer2_id)
            # TO AMMAR YASSER THESE IS HOW WE ADD IN PIPELINE STATE V2
            # pipeline_state_map = {
            #     "stt": STTState,
            #     "preprocessing": PreProcessingState,
            #     "processing": ProcessingState,
            #     "filler": FillerState,
            #     "tts": TTSState,
            # }
            # state_type = getattr(state_config, 'type', None)
            # PipelineStateClass = pipeline_state_map.get(state_type)
            # if PipelineStateClass is not None:
            #     # For STT, pass input_data; for others, pass {}
            #     if state_type == "stt":
            #         pipeline_input = input_data
            #     else:
            #         pipeline_input = {}
            #     state = PipelineStateClass(
            #         state_id=self.current_workflow_state_id,
            #         agent_registry=self.agent_registry,
            #         session_id=self.session_id
            #     )
            #     result = await state.process(pipeline_input, {
            #         "session_id": self.session_id,
            #         "user_id": self.user_id,
            #         "account_id": "12345"
            #     })

            state = State(
                    state_id=self.current_workflow_state_id,
                    config=state_config,
                    layer2_config=layer2_config,
                    memory=self.memory_manager,
                    tools_registry=self.agent_registry
                )
            result = await state.execute(input_data, {
                    "session_id": self.session_id,
                    "user_id": self.user_id,
                    "account_id": "12345"
                })
            await self.memory_manager.set("contextual", "output", result.outputs)
            self.execution_history.append(result)
            previous_state = self.current_workflow_state_id

            # Calculate execution duration
            duration_ms = (time.perf_counter() - start_time) * 1000

            # Merge duration with existing metrics
            execution_metrics = result.meta.get("metrics", {})
            execution_metrics["state_manager_duration_ms"] = duration_ms

            logger.info(
                "State execution completed",
                action="execute_step",
                output_data={
                    "previous_state": previous_state,
                    "next_state": self.current_workflow_state_id,
                    "result_status": result.status.value if hasattr(result.status, 'value') else str(result.status)
                },
                layer="state_management",
                metrics=execution_metrics
            )

            # Update logger context for new state
            logger.update_context(state_id=self.current_workflow_state_id)

            return result

        except Exception as e:
            # Calculate duration even on error
            duration_ms = (time.perf_counter() - start_time) * 1000

            logger.error(
                "Error during state execution",
                action="execute_step",
                input_data={"current_state": self.current_workflow_state_id, "input": input_data},
                reason=str(e),
                layer="state_management",
                metrics={"duration_ms": duration_ms}
            )
            raise

    async def executePipelineState(self, input_data: dict={}) -> StateOutput:
        """
        Executes a single pipeline state step using the current pipeline step ID.

        Args:
            input_data: Input data for the pipeline step.

        Returns:
            StateOutput: Result of the pipeline execution.

        Raises:
            WorkflowError: If the current pipeline step is not found or execution fails.
        """
        start_time = time.perf_counter()

        if not self.current_pipeline_step_id:
            raise WorkflowError("No current pipeline step set.")

        if self.current_pipeline_step_id == "stt":
            while self.audioPlayer.turn == "ai" or self.audioPlayer.isAudioPlaying():
                await asyncio.sleep(0.1)
            self.audioPlayer.turn = "ai"
        
        # Update logger context for current pipeline step
        logger.update_context(session_id=self.session_id, state_id=self.current_pipeline_step_id)

        logger.info(
            "Starting pipeline state execution",
            action="executePipelineState",
            input_data={"current_step": self.current_pipeline_step_id, "input": input_data},
            layer="state_management"
        )

        try:
            
            workFlowStateConfig = self.get_state(self.current_workflow_state_id)
            if not workFlowStateConfig:
                raise WorkflowError(f"Current workflow state '{self.current_workflow_state_id}' not found in workflow.")
            layer2 = self.get_layer2(workFlowStateConfig.layer2_id)
            if not layer2:
                raise WorkflowError(f"Layer2 for state '{self.current_workflow_state_id}' not found in workflow.")
            
            processToExecute = None
            pipelineFullState: PipelineStep = None
            indexOfStep = 0
            for step in layer2.pipeline:
                if step.step == self.current_pipeline_step_id and indexOfStep >= self.indexOfCurrentPipelineStep:
                    processToExecute = step.process
                    pipelineFullState = step
                    break
                indexOfStep += 1

            if not processToExecute:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in Layer2 '{workFlowStateConfig.layer2_id}'.")
            
            if not pipelineFullState:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in Layer2 '{workFlowStateConfig.layer2_id}' pipeline.")

            pipeline_step: AbstractPipelineState = self.pipeline_state_map.get(processToExecute)
            if not pipeline_step:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in pipeline state map.")
            
            # Create an instance of the pipeline state class

            state:AbstractPipelineState = pipeline_step(
                state_id=self.current_pipeline_step_id,
                agent_registry=self.agent_registry,
                session_id=self.session_id
            )

            process_input_data = {}
            for key, value in pipelineFullState.input.items():
                savedVal = await self.memory_manager.get(f"{self.session_id}_{self.current_workflow_state_id}_{value}")
                if savedVal: 
                    process_input_data[key] = savedVal

            for key, value in input_data.items():
                process_input_data[key] = value
            # Execute the pipeline step
            # Temporarily until Ammarbasha finish his task that you can put custom input to workflow give specific text to tts operation

            if workFlowStateConfig.isFallbackInput and self.current_pipeline_step_id == "tts" and process_input_data.get("text") == None:
                expected_output_data = pipelineFullState.expected_output
                needed_data = ""
                for output in expected_output_data:
                    if await self.memory_manager.get(output) is None:
                        if needed_data != "":
                            needed_data += ", "
                        needed_data += output
                process_input_data["text"] = f"Pleass, you need to provide the following data to process your requests, the needed data are {needed_data}"
            if self.current_workflow_state_id == "ReverseTransferFunds" and self.current_pipeline_step_id == "tts" and process_input_data.get("text") == None:
                process_input_data["text"] = "Do you want to reverse this action ? Please say 'confirm' to proceed or 'cancel' to abort."
            if self.current_workflow_state_id == "CancelReverse" and self.current_pipeline_step_id == "tts" and process_input_data.get("text") == None:
                process_input_data["text"] = " The Reverse Operation has been canceled"
            if self.current_workflow_state_id == "ConfirmReverse" and self.current_pipeline_step_id == "tts" and process_input_data.get("text") == None:
                process_input_data["text"] = " The Reverse Operation was Successful"

            if self.current_pipeline_step_id == "processing":
                process_input_data["expected_output"] = pipelineFullState.expected_output

            result = await state.process(process_input_data, {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "account_id": "12345",
                "state_id": self.current_workflow_state_id,
                "persona": "test_persona"
            })



            # await self.memory_manager.set("contextual", f"process_{processToExecute}_output", result.outputs)
            for key, value in result.outputs.items():
                await self.memory_manager.set("contextual", f"{self.session_id}_{self.current_workflow_state_id}_{key}", value)

            if self.current_pipeline_step_id == "processing":
                # Save the variable outputs to memory
                if not result.outputs.get("variable_outputs") == None:
                    for var_key, var_value in result.outputs["variable_outputs"].items():
                        await self.memory_manager.set("contextual", var_key, var_value)

            # # Handle TTS results with interrupt support
            # if processToExecute == "tts_process" and result.outputs.get("audio_path"):
            #     try:
            #         logger.info("TTS pipeline step completed, handling audio playback with interrupt support")
            #         # Use the interrupt integration to play the TTS audio with monitoring
            #         interrupt_result = await self.interrupt_integration.handle_tts_with_real_concurrent_monitoring(
            #             tts_result=result,
            #             workflow_context={
            #                 "session_id": self.session_id,
            #                 "user_id": self.user_id,
            #                 "state_id": self.current_workflow_state_id,
            #                 "persona": "test_persona"
            #             }
            #         )

            #         # Update result with interrupt handling results
            #         if interrupt_result.status == StatusType.SUCCESS:
            #             result = interrupt_result
            #             logger.info("TTS played successfully with interrupt monitoring")
            #         else:
            #             logger.warning(f"Interrupt handling failed: {interrupt_result.message}")

            #     except Exception as interrupt_error:
            #         logger.error(f"Error in TTS interrupt handling: {interrupt_error}")
            #         # Continue with normal result if interrupt handling fails

            self.execution_history.append(result)

            # Track reversible actions
            await self._track_reversible_action(result)
            # Calculate pipeline execution duration
            duration_ms = (time.perf_counter() - start_time) * 1000

            logger.info(
                "Pipeline state execution completed",
                action="executePipelineState",
                output_data={
                    "step": self.current_pipeline_step_id,
                    "result_status": result.status.value if hasattr(result.status, 'value') else str(result.status)
                },
                layer="state_management",
                metrics={"duration_ms": duration_ms}
            )

            return result
        except Exception as e:
            # Calculate duration even on error
            duration_ms = (time.perf_counter() - start_time) * 1000

            logger.error(
                "Error during pipeline state execution",
                action="executePipelineState",
                input_data={"current_step": self.current_pipeline_step_id, "input": input_data},
                reason=str(e),
                layer="state_management",
                metrics={"duration_ms": duration_ms}
            )
            raise WorkflowError(f"Failed to execute pipeline state: {e}")
        
    async def executeBackChanneling(self, input_data: dict={}) -> StateOutput:
        try:
            pipeline_step: AbstractPipelineState = self.pipeline_state_map.get("filler_tts_process")
            if not pipeline_step:
                raise WorkflowError(f"Pipeline step '{self.current_pipeline_step_id}' not found in pipeline state map.")
            
            # Create an instance of the pipeline state class

            state:AbstractPipelineState = pipeline_step(
                state_id=self.current_pipeline_step_id,
                agent_registry=self.agent_registry,
                session_id=self.session_id
            )

            result = await state.process({}, {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "account_id": "12345",
                "state_id": self.current_workflow_state_id,
                "persona": "test_persona"
            })
            # self.execution_history.append(result)
            return result
        except Exception as e:
            logger.error(
                "Error during backchanneling execution",
                action="executeBackChanneling",
                input_data={"current_step": self.current_pipeline_step_id, "input": input_data},
                reason=str(e),
                layer="state_management"
            )
            raise WorkflowError(f"Failed to execute backchanneling: {e}")

            
    async def transitionWorkflow(self, next_state_id: str):
        # TODO: Implement Adding Additional Layer2 steps
        # TODO: load context, intent & allowed/disallowed actions for every workflow
        
        start_time = time.perf_counter()

        logger.info(
            "Starting workflow transition",
            action="transitionWorkflow",
            input_data={"current_state": self.current_workflow_state_id, "next_state": next_state_id},
            layer="state_management"
        )

        try:
            state_config = self.get_state(self.current_workflow_state_id)
            if not state_config:
                raise WorkflowError(f"Current state '{self.current_workflow_state_id}' not found in workflow.")
            # for transition in state_config.transitions:
            #     if transition.target == next_state_id:
            #         self.current_workflow_state_id = next_state_id
            #         return next_state_id

            next_state_config = self.get_state(next_state_id)

            for expected_input in next_state_config.expected_input:
                if await self.memory_manager.get(expected_input) is None:
                    if next_state_config.missing_input_fallback_state:
                        return await self.transitionWorkflow(next_state_config.missing_input_fallback_state)
                    return self.current_workflow_state_id
            
            # Handle dynamic anti-state resolution
            if next_state_id == "last_anti_state":
                actual_next_state = await self._resolve_anti_state()
                if actual_next_state:
                    next_state_id = actual_next_state
                else:
                    # No reversible action found, stay in current state
                    return self.current_workflow_state_id

            self.current_workflow_state_id = next_state_id
            if self.current_workflow_state_id:
                current_state = self.get_state(self.current_workflow_state_id)
                if current_state and hasattr(current_state, 'layer2_id'):
                    layer2 = self.layer2_map.get(current_state.layer2_id)
                    if layer2 and layer2.pipeline:
                        self.current_pipeline_step_id = layer2.pipeline[0].step
                        if layer2.initial_variable:
                            for variable, value in layer2.initial_variable.items():
                                await self.memory_manager.set("contextual", f"{self.session_id}_{self.current_workflow_state_id}_{variable}", value)

            
            print(f"next_state_id: {next_state_id}")
            print("getting out if condetion")
            # Handle state-specific initialization logic
            if next_state_id == "RepeatedUserQuery":
                print("getting in if condetion")
                await self.memory_manager.clear_context() 

            # Calculate transition duration
            duration_ms = (time.perf_counter() - start_time) * 1000

            logger.info(
                "Workflow transition completed",
                action="transitionWorkflow",
                output_data={"new_state": next_state_id, "new_pipeline_step": self.current_pipeline_step_id},
                layer="state_management",
                metrics={"duration_ms": duration_ms}
            )
            self.indexOfCurrentPipelineStep = 0
            return next_state_id
        except Exception as e:
            # Calculate duration even on error
            duration_ms = (time.perf_counter() - start_time) * 1000

            logger.error(
                "Error during workflow transition",
                action="transitionWorkflow",
                input_data={"current_state": self.current_workflow_state_id, "next_state": next_state_id},
                reason=str(e),
                layer="state_management",
                metrics={"duration_ms": duration_ms}
            )
            raise WorkflowError(f"Failed to transition workflow: {e}")

    async def _resolve_anti_state(self) -> str:
        """
        Resolve the appropriate anti-state based on the last reversible action.
        Returns the anti-state name or None if no reversible action found.
        """
        try:
            # Get the last reversible action from memory
            last_action = await self.memory_manager.get("last_reversible_action")
        
            if not last_action:
                logger.warning("No last reversible action found for undo")
                return None
        
            state_id = last_action.get("state_id")
        
            if not state_id:
                logger.warning("No state_id found in last reversible action")
                return None
            
            # Get the state configuration to find its anti_state
            state_config = self.get_state(state_id)
            if not state_config:
                logger.warning(f"State {state_id} not found in workflow")
                return None
            
            # Check if the state has an anti_state defined
            anti_state = getattr(state_config, 'anti_state', None)
            if not anti_state:
                logger.warning(f"No anti_state defined for {state_id}")
                return None
            
            # Check if anti_state exists in workflow
            if not self.get_state(anti_state):
                logger.warning(f"Anti_state {anti_state} not found in workflow")
                return None
            
            # Store the original action data for the anti-state to use
            await self.memory_manager.set("contextual", "undo_context", {
                "original_state": state_id,
                "original_outputs": last_action.get("outputs", {}),
                "timestamp": last_action.get("timestamp")
            })
            
            logger.info(
                f"Resolved anti_state {anti_state} for {state_id}",
                action="_resolve_anti_state",
                input_data={"original_state": state_id, "anti_state": anti_state},
                layer="state_management"
            )
            
            return anti_state
            
        except Exception as e:
            logger.error(
                "Error resolving anti_state",
                action="_resolve_anti_state",
                reason=str(e),
                layer="state_management"
            )
            return None


    async def transitionPipeline(self, next_state_id: str):
        try:
            state_config = self.get_state(self.current_workflow_state_id)
            if not state_config:
                raise WorkflowError(f"Current state '{self.current_workflow_state_id}' not found in workflow.")
            layer2 = self.layer2_map.get(state_config.layer2_id)
            if not layer2:
                raise WorkflowError(f"Layer2 for state '{self.current_workflow_state_id}' not found.")
            indexOfStep = 0
            for step in layer2.pipeline:
                if step.step == next_state_id and indexOfStep > self.indexOfCurrentPipelineStep:
                    self.indexOfCurrentPipelineStep = indexOfStep
                    self.current_pipeline_step_id = next_state_id
                    return next_state_id
                indexOfStep += 1
            raise WorkflowError(f"Pipeline step '{next_state_id}' not found in Layer2 '{state_config.layer2_id}'.")
        except Exception as e:
            logger.error(
                "Error during pipeline transition",
                action="transitionPipeline",
                input_data={"current_state": self.current_workflow_state_id, "next_step": next_state_id},
                reason=str(e),
                layer="state_management"
            )
            raise WorkflowError(f"Failed to transition pipeline: {e}")

    async def _track_reversible_action(self, result: StateOutput):
        """Track reversible actions for undo functionality"""
        try:
            state_config = self.get_state(self.current_workflow_state_id)
            if not state_config:
                return

            # Check if state has anti_state (implies reversibility)
            if hasattr(state_config, 'anti_state') and state_config.anti_state:
                from datetime import datetime

                action_record = {
                    "state_id": self.current_workflow_state_id,
                    "timestamp": datetime.now().isoformat(),
                    "outputs": result.outputs,  # Store the outputs for reversal
                    "anti_state": state_config.anti_state
                }

                # Adding reversible actions to memory
                await self.memory_manager.set("contextual", "last_reversible_action", action_record)

                logger.info(
                    f"Tracked reversible action: {self.current_workflow_state_id}",
                    action="_track_reversible_action",
                    input_data={"state_id": self.current_workflow_state_id},
                    layer="state_management"
                )

        except Exception as e:
            logger.warning(
                f"Failed to track reversible action: {e}",
                action="_track_reversible_action",
                reason=str(e),
                layer="state_management"
            )


    async def getWorkflow(self):  # TODO: -> WorkflowWrapper:
        """
        Returns the current workflow being executed.
        
        Returns:
            WorkflowWrapper: The current workflow instance.
        """
        if not self.workflow:
            raise WorkflowError("No workflow loaded.")
        return self.workflow
    
    async def getFullPipelineMap(self):  # TODO: -> List[Layer2]:
        """
        Returns a list of all Layer2 pipelines in the workflow.
        
        Returns:
            List[Layer2]: List of Layer2 pipeline definitions.
        """
        if not self.layer2_map:
            raise WorkflowError("No Layer2 pipelines loaded.")
        return list(self.layer2_map.values())
    
    async def getCurrentPipeline(self):  # TODO: -> Layer2:
        """
        Returns the current Layer2 pipeline for the current workflow state.
        
        Returns:
            Layer2: The current Layer2 pipeline definition.
        """
        if not self.current_workflow_state_id:
            raise WorkflowError("No current workflow state set.")
        state_config = self.get_state(self.current_workflow_state_id)
        if not state_config:
            raise WorkflowError(f"Current state '{self.current_workflow_state_id}' not found in workflow.")
        layer2 = self.get_layer2(state_config.layer2_id)
        if not layer2:
            raise WorkflowError(f"Layer2 for state '{self.current_workflow_state_id}' not found in workflow.")
        return layer2
    
    async def getCurrentWorkflowState(self) -> str:
        """
        Returns the ID of the current workflow state.
        
        Returns:
            str: The ID of the current workflow state.
        """
        if not self.current_workflow_state_id:
            raise WorkflowError("No current workflow state set.")
        return self.current_workflow_state_id
    
    async def getCurrentPipelineState(self) -> str:
        """
        Returns the ID of the current pipeline step.
        
        Returns:
            str: The ID of the current pipeline step.
        """
        if not self.current_pipeline_step_id:
            raise WorkflowError("No current pipeline step set.")
        return self.current_pipeline_step_id
    
    async def getProhibitedActions(self) -> List[str]:
        """
        Returns a list of prohibited actions for the current workflow state.
        
        Returns:
            List[str]: List of prohibited actions.
        """
        if not self.workflow.workflow:
            raise WorkflowError("No workflow loaded.")
        return self.workflow.workflow.prohibited_actions
    
    async def getAllowedActions(self) -> List[str]:
        """
        Returns a list of allowed actions for the current workflow state.
        
        Returns:
            List[str]: List of allowed actions.
        """
        if not self.workflow.workflow:
            raise WorkflowError("No workflow loaded.")
        return self.workflow.workflow.allowed_actions
    
    async def isBackChannelingEnabled(self) -> bool:
        """
        Checks if back-channeling is enabled for the current workflow.
        
        Returns:
            bool: True if back-channeling is enabled, False otherwise.
        """
        pipeline: Layer2 = await self.getCurrentPipeline()
        if not pipeline:
            raise WorkflowError("No current pipeline loaded.")
        return pipeline.backChanneling
    
    async def getCurrentPipelineStepIndex(self):
        """        
            Returns the index of the current pipeline step in the Layer2 pipeline.
            This is used to track the progress of the pipeline execution.
            
            Returns:
                int: The index of the current pipeline step.
        """
        return self.indexOfCurrentPipelineStep

    # todo : built data st to save the states and move the function to memory manager 
    async def _update_session_metadata_after_execution(self, previous_state: str, result):
        """
        Update session metadata in contextual memory after state execution.
        """
        try:
            # Update current state
            await self.memory_manager.set("contextual", "current_state", self.current_state_id)

            # Track execution result status
            if hasattr(result, 'status'):
                status_str = result.status.value if hasattr(result.status, 'value') else str(result.status)
                if status_str in ['FAILURE', 'ERROR']:
                    # Track interrupts/errors
                    interrupts = await self.memory_manager.get("interrupts") or []
                    interrupts.append({
                        "state": previous_state,
                        "error": status_str,
                        "timestamp": datetime.now().isoformat()
                    })
                    await self.memory_manager.set("contextual", "interrupts", interrupts)

        except Exception as e:
            logger.error(
                "Error updating session metadata after execution",
                action="_update_session_metadata_after_execution",
                reason=str(e),
                layer="state_management"
            )

    async def _update_session_metadata_after_transition(self, previous_state: str, next_state: str):
        """
        Update session metadata in contextual memory after state transition.
        """
        try:
            from datetime import datetime

            # Update current state
            await self.memory_manager.set("contextual", "current_state", next_state)

            # Add to states_visited
            states_visited = await self.memory_manager.get("states_visited") or []
            if next_state not in states_visited:
                states_visited.append(next_state)
                await self.memory_manager.set("contextual", "states_visited", states_visited)

            # Check if this is a final state (no transitions)
            next_state_config = self.get_state(next_state)
            if next_state_config and (not next_state_config.transitions or len(next_state_config.transitions) == 0):
                await self.memory_manager.set("contextual", "outcome", "completed")
                await self.memory_manager.set("contextual", "final_action", f"reached_final_state_{next_state}")

            logger.debug(
                "Updated session metadata after transition",
                action="_update_session_metadata_after_transition",
                input_data={"previous_state": previous_state, "next_state": next_state},
                layer="state_management"
            )

        except Exception as e:
            logger.error(
                "Error updating session metadata after transition",
                action="_update_session_metadata_after_transition",
                reason=str(e),
                layer="state_management"
            )

    async def end_session_cleanup(self):
        """
        Clean up session and mark it as completed.
        """
        try:
            # Mark session as completed
            await self.memory_manager.set("contextual", "outcome", "completed")
            await self.memory_manager.set("contextual", "session_end_time", datetime.now().isoformat())

            # Set final action if not already set
            final_action = await self.memory_manager.get("final_action")
            if not final_action:
                await self.memory_manager.set("contextual", "final_action", "session_cleanup")

            logger.info(
                "Session cleanup completed",
                action="end_session_cleanup",
                layer="state_management"
            )

        except Exception as e:
            logger.error(
                "Error during session cleanup",
                action="end_session_cleanup",
                reason=str(e),
                layer="state_management"
            )

        # Example function

    # async def example_memory_usage(self):
    #     """
    #     Example of how to use the memory manager for all memory layers in StateManager.
    #     """
    #     # --- Ephemeral memory: store temporary pipeline data ---
    #     await self.memory_manager.set("ephemeral", "transcribed_text", "Check my balance")
    #     # Retrieve ephemeral data
    #     transcribed = await self.memory_manager.get("transcribed_text")
    #     # Clear ephemeral after pipeline
    #     await self.memory_manager.clear_ephemeral()

    #     # --- Contextual memory: store session data and conversation ---
    #     await self.memory_manager.set("contextual", "user_message", "Check my balance")
    #     await self.memory_manager.set("contextual", "intent", "check_balance")
    #     await self.memory_manager.set("contextual", "slots", {"account_id": "12345"})
    #     # Store conversation turns
    #     conversation = await self.memory_manager.get("conversation") or []
    #     conversation.append({"role": "user", "text": "Check my balance 2"})
    #     conversation.append({"role": "ai", "text": "Sure, I'll check your balance 2"})
    #     await self.memory_manager.set("contextual", "conversation", conversation)
    #     # Save contextual memory to file for logging/debugging
    #     await self.memory_manager.contextual.save_to_file("contextual_memory_log.json")
    #     conversation.append({"role": "user", "text": "Check my balance 1"})
    #     conversation.append({"role": "ai", "text": "Sure, I'll check your balance 1"})
    #     await self.memory_manager.set("contextual", "conversation", conversation)
    #     # Clear contextual at end of session
    #     # await self.memory_manager.clear_contextual()
    #     # Log memory state for debugging
    #     logger.debug(
    #         "Current contextual memory state",
    #         action="example_memory_usage",
    #         output_data={
    #             "conversation": await self.memory_manager.contextual.get("conversation"),
    #             "all_contextual": await self.memory_manager.contextual.get_all()
    #         },
    #         layer="state_management"
    #     )

    #     # --- Persistent memory: store long-term user data ---
    #     await self.memory_manager.set("persistent", "validated_account_id", "12345")
    #     await self.memory_manager.set("persistent", "account_balance_history", {"2025-06-17": 5000})
    #     # Retrieve persistent data
    #     balance_history = await self.memory_manager.get("account_balance_history")
    #     # Explicit memory saving (e.g., user says "Remember my preferred language is English")
    #     await self.memory_manager.explicit_save("save_preference", {"preference": "language", "value": "en"})
    #     # Retrieve preference
    #     language = await self.memory_manager.get("language")


    # Example function
    # async def handle_intent(self, intent):
    #     """Example handler to show when to clear memory."""
    #     if intent == "goodbye":
    #         await self.end_session_cleanup()
    #         # Transition to end state, etc.


    # TODO : INVESTEGATE IF BLOAT 
    async def check_and_persist_workflow_summary(self):
        """
        Checks if the workflow summary in persistent memory is up to date with the current workflow version.
        Only persists the summary if the version is different or summary does not exist.
        """
        if self.workflow and hasattr(self.workflow, 'workflow'):
            workflow_id = self.workflow.workflow.id
            new_version = self.workflow.workflow.version
            existing = await self.memory_manager.get(f"workflow_source_of_truth_{workflow_id}")
            existing_version = None
            if existing and isinstance(existing, dict):
                existing_version = existing.get("version")
            if existing_version != new_version:
                await self.memory_manager.persist_workflow_summary(self.workflow.workflow)
                logger.info(
                    f"Persisted workflow summary for {workflow_id} (version {new_version}) to persistent memory.",
                    action="persist_workflow_source_of_truth",
                    layer="state_management"
                )
            else:
                logger.info(
                    f"Workflow summary for {workflow_id} already up to date (version {new_version}). Skipping persist.",
                    action="persist_workflow_source_of_truth_skip",
                    layer="state_management"
                )



#################################### interrupt system functions  ##############################


    async def trigger_interrupt(self, user_input: str) -> StateOutput:
        """
        Trigger an interrupt with user input using the simple interrupt integration.
        """
        try:
            if not self.interrupt_integration:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Interrupt integration not initialized",
                    code=StatusCode.INTERNAL_ERROR,
                    outputs={},
                    meta={}
                )

            return await self.interrupt_integration.trigger_interrupt(user_input)

        except Exception as e:
            logger.error(
                "Error triggering interrupt",
                action="trigger_interrupt",
                reason=str(e),
                layer="state_management"
            )
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Interrupt trigger error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )







