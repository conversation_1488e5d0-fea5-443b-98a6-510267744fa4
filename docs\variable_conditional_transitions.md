# Variable-Based Conditional Transitions

## Overview

The Voice Agents Platform now supports variable-based conditional transitions in workflow JSON files. This enhancement allows workflows to make transition decisions based on variable values stored in memory, in addition to the existing intent-based transitions.

## Features

### Supported Condition Types

1. **Intent-based conditions** (existing):
   ```json
   {
     "condition": "intent == 'account_balance'",
     "target": "CheckBalance"
   }
   ```

2. **Variable-based conditions** (new):
   ```json
   {
     "condition": "amount >= 60000",
     "target": "HighValueTransferApproval"
   }
   ```

3. **Combined conditions** (new):
   ```json
   {
     "condition": "intent == 'fund_transfer' and amount >= 60000",
     "target": "HighValueTransferApproval"
   }
   ```

### Supported Operators

- **Comparison**: `==`, `!=`, `<`, `<=`, `>`, `>=`
- **Logical**: `and`, `or`, `not`
- **Membership**: `in` (for lists/sets)
- **String operations**: String equality and comparison

### Variable Resolution

Variables are resolved from memory using the following key formats (in order of priority):

1. `{session_id}_{workflow_state}_{variable}` - Session and workflow scoped
2. `{workflow_state}_{variable}` - Workflow scoped
3. `{variable}` - Simple key
4. `contextual_{variable}` - Contextual prefix

## Implementation Details

### Core Components

1. **VariableConditionParser** (`core/state_manager/variable_condition_parser.py`):
   - Extracts variable names from condition strings
   - Builds evaluation contexts from memory
   - Handles variable resolution with multiple key formats

2. **Enhanced OrchestratorV3** (`agents/orchestrator_agent_v3.py`):
   - Integrates variable condition parsing
   - Evaluates conditions before LLM fallback
   - Maintains backward compatibility

### Evaluation Process

1. **Variable Extraction**: Parse condition strings to identify variables
2. **Context Building**: Retrieve variable values from memory
3. **Condition Evaluation**: Use asteval interpreter to evaluate conditions
4. **Transition Selection**: Return first satisfied condition or fall back to LLM

## Usage Examples

### Example 1: High-Value Transfer Approval

```json
{
  "TransferFunds": {
    "id": "TransferFunds",
    "type": "transaction",
    "transitions": [
      {
        "condition": "amount >= 60000",
        "target": "HighValueTransferApproval"
      },
      {
        "condition": "balance < amount",
        "target": "InsufficientFundsError"
      },
      {
        "condition": "true",
        "target": "RepeatedUserQuery"
      }
    ]
  }
}
```

### Example 2: Account Type-Based Routing

```json
{
  "CustomerService": {
    "id": "CustomerService",
    "transitions": [
      {
        "condition": "account_type == 'premium' and balance >= 100000",
        "target": "PremiumCustomerService"
      },
      {
        "condition": "account_type == 'business'",
        "target": "BusinessCustomerService"
      },
      {
        "condition": "true",
        "target": "StandardCustomerService"
      }
    ]
  }
}
```

### Example 3: Time-Based Conditions

```json
{
  "ServiceHours": {
    "id": "ServiceHours",
    "transitions": [
      {
        "condition": "current_hour >= 9 and current_hour <= 17",
        "target": "BusinessHoursService"
      },
      {
        "condition": "true",
        "target": "AfterHoursService"
      }
    ]
  }
}
```

## Memory Management

### Setting Variables

Variables should be set in contextual memory using the session and workflow scoped format:

```python
# In agent code
await memory_manager.set("contextual", f"{session_id}_{workflow_state}_amount", 75000)
await memory_manager.set("contextual", f"{session_id}_{workflow_state}_balance", 100000)
await memory_manager.set("contextual", f"{session_id}_{workflow_state}_account_type", "premium")
```

### Variable Types

Supported variable types:
- **Numbers**: `int`, `float`
- **Strings**: Text values
- **Booleans**: `True`, `False`
- **Lists/Sets**: For membership testing

## Backward Compatibility

The system maintains full backward compatibility:

1. **Existing workflows** continue to work without modification
2. **Intent-only conditions** are processed as before
3. **LLM fallback** is used when variable conditions fail
4. **Simple conditions** like `"true"` and `"false"` work unchanged

## Error Handling

### Graceful Degradation

- **Missing variables**: Conditions evaluate to `False`
- **Invalid conditions**: Fall back to LLM evaluation
- **Evaluation errors**: Log warnings and continue with next condition

### Logging

Comprehensive logging is provided for:
- Variable extraction
- Context building
- Condition evaluation
- Transition decisions

## Testing

Run the test suite to validate functionality:

```bash
python tests/test_variable_conditional_transitions.py
```

Test coverage includes:
- Variable extraction from various condition formats
- Context building from memory
- Condition evaluation with real data
- Integration with workflow transitions

## Best Practices

1. **Condition Order**: Place more specific conditions before general ones
2. **Variable Naming**: Use clear, descriptive variable names
3. **Error Handling**: Always include a fallback condition (`"true"`)
4. **Memory Keys**: Use consistent key formats for variable storage
5. **Testing**: Test conditions with various data scenarios

## Migration Guide

### Updating Existing Workflows

1. **Identify transition points** where variable conditions would be beneficial
2. **Add variable-based conditions** before existing intent conditions
3. **Ensure variables are set** in the appropriate agents/states
4. **Test thoroughly** with various data scenarios

### Example Migration

Before:
```json
{
  "condition": "intent == 'fund_transfer'",
  "target": "TransferFunds"
}
```

After:
```json
[
  {
    "condition": "intent == 'fund_transfer' and amount >= 60000",
    "target": "HighValueTransferApproval"
  },
  {
    "condition": "intent == 'fund_transfer'",
    "target": "TransferFunds"
  }
]
```
