#!/usr/bin/env python3
"""
Simple test to verify per-call console output control works correctly.
"""

import sys
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger, LoggerConfig

def test_per_call_console_control():
    """Test the per-call console output control functionality."""
    print("🧪 Testing Per-Call Console Output Control")
    print("=" * 50)
    
    # Set up logger
    LoggerConfig.setup_for_development()
    logger = get_module_logger("test_module", session_id="test_session")
    
    print("\n1. Testing default behavior (only warnings/errors in console):")
    logger.debug("🔍 This debug message should NOT show in console")
    logger.info("ℹ️ This info message should NOT show in console")
    logger.warning("⚠️ This warning message SHOULD show in console")
    logger.error("❌ This error message SHOULD show in console")
    
    print("\n2. Testing console_output='all' (force all to show):")
    logger.debug("🔍 This debug message SHOULD show in console", console_output="all")
    logger.info("ℹ️ This info message SHOULD show in console", console_output="all")
    logger.warning("⚠️ This warning message SHOULD show in console", console_output="all")
    logger.error("❌ This error message SHOULD show in console", console_output="all")
    
    print("\n3. Testing console_output='false' (suppress console):")
    logger.warning("⚠️ This warning should NOT show in console", console_output="false")
    logger.error("❌ This error should NOT show in console", console_output="false")
    
    print("\n4. Testing console_output='error' (only errors):")
    logger.debug("🔍 This debug should NOT show in console", console_output="error")
    logger.info("ℹ️ This info should NOT show in console", console_output="error")
    logger.warning("⚠️ This warning should NOT show in console", console_output="error")
    logger.error("❌ This error SHOULD show in console", console_output="error")
    
    print("\n5. Testing structured logging with console control:")
    logger.info(
        "📊 Structured log with forced console output",
        action="test_action",
        input_data={"test": "data"},
        layer="test_layer",
        console_output="all"
    )
    
    logger.debug(
        "🔍 Structured debug with suppressed console",
        action="test_action",
        input_data={"debug": "info"},
        layer="test_layer",
        console_output="false"
    )
    
    print("\n✅ Test completed!")
    print("Check the log files for all messages (they should all be there).")
    print("Console output should match the expected behavior above.")

if __name__ == "__main__":
    test_per_call_console_control() 