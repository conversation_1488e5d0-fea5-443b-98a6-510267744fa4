import asyncio
import os
import sys
import time
import wave
import numpy as np
from core.session_manager_v2 import SessionManagerV2
import sounddevice as sd
from dotenv import load_dotenv

try:
    import webrtcvad
    WEBRTCVAD_AVAILABLE = True
except ImportError:
    WEBRTCVAD_AVAILABLE = False
    print("⚠️ webrtcvad not available - VAD functionality will be limited")

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

load_dotenv()

async def select_input_device():
    """Select audio input device for microphone recording."""
    print("\n🎤 Available Audio Input Devices:")
    devices = sd.query_devices()
    input_devices = []
    
    for i, device in enumerate(devices):
        if device['max_input_channels'] > 0:
            input_devices.append((i, device))
            print(f"  {len(input_devices)}. {device['name']} (Device {i})")
    
    if not input_devices:
        print("❌ No input devices found!")
        return None
    
    # Add default option
    print(f"  0. Use Default Microphone (recommended)")

    while True:
        try:
            choice = input(f"Select input device (0 for default, 1-{len(input_devices)}): ").strip()

            if choice == "0" or choice == "":
                print("✅ Selected: Default microphone device")
                return None  # None means use default device

            device_num = int(choice)
            if 1 <= device_num <= len(input_devices):
                device_index = input_devices[device_num - 1][0]
                device_name = input_devices[device_num - 1][1]['name']
                print(f"✅ Selected: {device_name}")
                return device_index
            else:
                print(f"Please enter 0 for default or a number between 1 and {len(input_devices)}")
        except ValueError:
            print("Please enter a valid number or 0 for default")
        except KeyboardInterrupt:
            print("\n❌ Device selection cancelled")
            return None




async def run_turn_based_conversation_workflow():
    """
    Run a simplified turn-based conversation workflow.
    
    SIMPLIFIED APPROACH:
    ===================
    The test simply calls orchestrator.run() once and lets the orchestrator 
    handle the entire conversation flow internally. The STT state will 
    automatically capture microphone input when needed.
    
    FLOW (handled internally by orchestrator):
    - Greeting TTS → STT (auto-captures mic) → Preprocessing → Processing → TTS
    - Repeat: STT (auto-captures mic) → Preprocessing → Processing → TTS  
    - Continue until Goodbye state
    """
    print("🎯 Turn-Based Conversation Workflow Test")
    print("=" * 50)
    print("📋 The orchestrator will handle the entire conversation flow internally")
    print("🎤 STT states will automatically prompt for microphone input")
    print()

    # Select microphone device for the STT states to use
    device_index = await select_input_device()
    if device_index is None:
        print("❌ Cannot proceed without microphone device")
        return
    print("✅ Microphone device selected")

    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow_v2.json'
    user_id = 'turn_based_conversation_user'
    session_id = None  # Initialize session_id to None

    try:
        # Create a new session
        session_id = await session_manager.create_session(workflow_name, user_id)
        print(f"✅ Session created: {session_id}")

        # Store the device index in memory for STT agent to use
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]
        await memory_manager.set("contextual", "microphone_device_index", device_index)

        # Initialize orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)

        print("\n🎭 Starting turn-based conversation...")
        print("🤖 The orchestrator will now handle the entire conversation flow")
        print("🎤 You'll be prompted to speak when the STT state needs input")
        print()

        # Single orchestrator call - let it handle everything internally
        result = await orchestrator.run()
        
        # Display final result
        print(f"\n🏁 Conversation completed!")
        print(f"   Status: {result.get('status', 'unknown')}")
        if result.get('reason'):
            print(f"   Details: {result.get('reason')}")
        if result.get('key_events'):
            print(f"   Key Events: {result.get('key_events')}")

        print("\n🎉 Turn-based conversation workflow completed!")

    except Exception as e:
        print(f"❌ Error in workflow: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Clean up the session if it was created
        if session_id:
            await session_manager.cleanup_session(session_id, reason="turn_based_conversation_complete")
            print("✅ Session cleaned up. Test complete!")
        else:
            print("✅ No session to clean up. Test complete!")


if __name__ == "__main__":
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))
    print("🚀 Turn-Based Conversation with AUTOMATIC Interrupt Monitoring")
    print("This test demonstrates a complete voice pipeline with built-in interrupt handling:")
    print("  ✅ Microphone input with VAD (Voice Activity Detection)")
    print("  ✅ Turn-based conversation flow: AI speaks → User speaks → AI responds")
    print("  ✅ Complete STT → Preprocessing → Processing → TTS workflow")
    print("  🎤 AUTOMATIC interrupt monitoring during TTS playback")
    print("  ⏸️ Real-time TTS pause when user interrupts")
    print("  🔄 Action reversibility checking and conditional queuing")
    print("  ✅ Automatic state transitions based on user intent")
    print("  ✅ Natural conversation ending (Goodbye state)")
    print()
    print("💡 TIP: While the AI is speaking, try interrupting by talking!")
    print("    The system will automatically pause TTS and handle your interrupt.")
    print()

    # Run the turn-based conversation test
    asyncio.run(run_turn_based_conversation_workflow())
