import os
import json
import asyncio
import time
from statistics import mean
from typing import Tu<PERSON>, Dict, Any

import openai

# Local simple cleaner to avoid agent dependency
def simple_clean_text(text: str) -> str:
    return " ".join(text.strip().lower().split())

# Minimal .env loader (no external dependency)
def load_env_fallback():
    try:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        candidates = [
            os.path.join(os.getcwd(), ".env"),
            os.path.join(base_dir, ".env"),
            os.path.join(os.path.dirname(base_dir), ".env"),
            os.path.join(os.path.dirname(os.path.dirname(base_dir)), ".env"),
        ]
        for path in candidates:
            if os.path.isfile(path):
                with open(path, "r", encoding="utf-8") as f:
                    for line in f:
                        line = line.strip()
                        if not line or line.startswith("#"):
                            continue
                        if "=" not in line:
                            continue
                        key, val = line.split("=", 1)
                        key = key.strip()
                        val = val.strip().strip('"').strip("'")
                        if key and key not in os.environ:
                            os.environ[key] = val
                break
    except Exception:
        # Silent fallback; we still rely on real env if present
        pass


COMBINED_PROMPT_PREFIX = """You are an AI assistant for a banking voice agent platform.
Your job is to analyze a user's message and determine:

1. **Intent** — One of:
   - account_balance (checking account balance)
   - fund_transfer (transferring money)
   - exchange_rate (currency exchange)
   - goodbye (ending conversation)
   - undo (undoing last action)
   - confirm (confirming an action)
   - cancel (canceling an action)
   - unknown (if none match)

2. **Emotion** — The primary emotion from:
   - neutral
   - happy
   - sad
   - angry
   - frustrated
   - excited
   - surprised
   - other

3. **Gender** — The most likely gender of the speaker:
   - male
   - female
   - unknown

For each category, provide:
- The detected value
- A confidence score between 0 and 1 (float)

Respond **only** in strict JSON format like this:

{
  "intent": "<intent>",
  "intent_confidence": <float>,
  "emotion": "<emotion>",
  "emotion_confidence": <float>,
  "gender": "<male/female/unknown>",
  "gender_confidence": <float>
}"""


TEST_UTTERANCE = "Listen, I’m honestly a bit frustrated — last time you tried sending money to my cousin it failed, and I really need you to transfer $750 to her account right now before the bank closes. Also, after that, could you tell me my current balance?"


def extract_json(s: str) -> str:
    s = s.strip()
    # Strip markdown code fences if present
    if s.startswith("```"):
        s = s.strip("` \n")
    # Extract the JSON substring if extra text exists
    start = s.find("{")
    end = s.rfind("}")
    if start != -1 and end != -1 and end > start:
        return s[start : end + 1]
    return s


async def single_call_combined(client: openai.AsyncOpenAI, clean_text: str) -> Tuple[Dict[str, Any], float, Dict[str, int]]:
    prompt = COMBINED_PROMPT_PREFIX + f"\n\nMessage: \"\"\"{clean_text}\"\"\""
    print(f"[Single call prompt]: {prompt}")
    print("-" * 80)
    t0 = time.perf_counter()
    resp = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.1,
        max_tokens=200,
    )
    latency_ms = (time.perf_counter() - t0) * 1000
    raw = resp.choices[0].message.content
    parsed = extract_json(raw)
    try:
        data = json.loads(parsed)
    except Exception:
        data = {}
    usage = getattr(resp, "usage", None) or {}
    usage_dict = {
        "prompt_tokens": int(getattr(usage, "prompt_tokens", 0) or usage.get("prompt_tokens", 0) or 0),
        "completion_tokens": int(getattr(usage, "completion_tokens", 0) or usage.get("completion_tokens", 0) or 0),
        "total_tokens": int(getattr(usage, "total_tokens", 0) or usage.get("total_tokens", 0) or 0),
    }
    return data, latency_ms, usage_dict


async def call_intent(client: openai.AsyncOpenAI, text: str):
    prompt = (
        "Given the following user message, identify the user's intent. Use these specific intent values:\n"
        "- account_balance: (checking account balance)\n"
        "- fund_transfer: (transferring money)\n"
        "- exchange_rate:  (currency exchange)\n"
        "- goodbye (ending conversation)\n"
        "- undo (undoing last action)\n"
        "- confirm (confirming an action)\n"
        "- cancel (canceling an action)\n"
        "- unknown (if none match)\n\n"
        "Respond only in JSON: {\"intent\": <intent>, \"intent_confidence\": <float between 0 and 1>}\n"
        f"Message: {text}\nIntent:"
    )
    print(f"[intent prompt]: {prompt}")
    print("-" * 80)
    resp = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=50,
        temperature=0.2,
    )
    usage = getattr(resp, "usage", None) or {}
    raw = resp.choices[0].message.content.strip()
    try:
        payload = json.loads(extract_json(raw))
    except Exception:
        payload = {"intent": "unknown", "intent_confidence": 0.0}
    return payload, {
        "prompt_tokens": int(getattr(usage, "prompt_tokens", 0) or usage.get("prompt_tokens", 0) or 0),
        "completion_tokens": int(getattr(usage, "completion_tokens", 0) or usage.get("completion_tokens", 0) or 0),
        "total_tokens": int(getattr(usage, "total_tokens", 0) or usage.get("total_tokens", 0) or 0),
    }

async def call_emotion(client: openai.AsyncOpenAI, text: str):
    prompt = (
        "You are an AI assistant for a banking voice agent platform.\n"
        "Determine the primary emotion expressed in the following message from this list:\n"
        "- neutral\n- happy\n- sad\n- angry\n- frustrated\n- excited\n- surprised\n- other\n\n"
        "Respond only in strict JSON format like this:\n"
        "{\"emotion\": \"<emotion>\", \"emotion_confidence\": <float>}\n\n"
        f"Message: \"{text}\""
    )
    print(f"[emotion prompt]: {prompt}")
    print("-" * 80)
    resp = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=50,
        temperature=0.3,
    )
    usage = getattr(resp, "usage", None) or {}
    raw = resp.choices[0].message.content.strip()
    try:
        payload = json.loads(extract_json(raw))
    except Exception:
        payload = {"emotion": None, "emotion_confidence": 0.0}
    return payload, {
        "prompt_tokens": int(getattr(usage, "prompt_tokens", 0) or usage.get("prompt_tokens", 0) or 0),
        "completion_tokens": int(getattr(usage, "completion_tokens", 0) or usage.get("completion_tokens", 0) or 0),
        "total_tokens": int(getattr(usage, "total_tokens", 0) or usage.get("total_tokens", 0) or 0),
    }

async def call_gender(client: openai.AsyncOpenAI, text: str):
    prompt = (
        "You are an AI assistant for a banking voice agent platform.\n"
        "Determine the most likely gender of the speaker from: male, female, unknown.\n\n"
        "Respond only in strict JSON format like this:\n"
        "{\"gender\": \"<male/female/unknown>\", \"gender_confidence\": <float>}\n\n"
        f"Message: \"{text}\""
    )
    print(f"[gender prompt]: {prompt}")
    print("-" * 80)
    resp = await client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=50,
        temperature=0.3,
    )
    usage = getattr(resp, "usage", None) or {}
    raw = resp.choices[0].message.content.strip()
    try:
        payload = json.loads(extract_json(raw))
    except Exception:
        payload = {"gender": None, "gender_confidence": 0.0}
    return payload, {
        "prompt_tokens": int(getattr(usage, "prompt_tokens", 0) or usage.get("prompt_tokens", 0) or 0),
        "completion_tokens": int(getattr(usage, "completion_tokens", 0) or usage.get("completion_tokens", 0) or 0),
        "total_tokens": int(getattr(usage, "total_tokens", 0) or usage.get("total_tokens", 0) or 0),
    }

async def triple_call_parallel(client: openai.AsyncOpenAI, clean_text: str) -> Tuple[Dict[str, Any], float, Dict[str, int]]:
    t0 = time.perf_counter()
    # Run three calls in parallel against the same clean_text
    intent_task = call_intent(client, clean_text)
    emotion_task = call_emotion(client, clean_text)
    gender_task = call_gender(client, clean_text)
    (intent_res, intent_usage), (emotion_res, emotion_usage), (gender_res, gender_usage) = await asyncio.gather(
        intent_task, emotion_task, gender_task
    )
    latency_ms = (time.perf_counter() - t0) * 1000

    out = {
        "intent": intent_res.get("intent", "unknown"),
        "intent_confidence": float(intent_res.get("intent_confidence", 0.0) or 0.0),
        "emotion": emotion_res.get("emotion", "neutral"),
        "emotion_confidence": float(emotion_res.get("emotion_confidence", 0.0) or 0.0),
        "gender": gender_res.get("gender", "unknown"),
        "gender_confidence": float(gender_res.get("gender_confidence", 0.0) or 0.0),
    }

    usage_dict = {
        "prompt_tokens": int(intent_usage.get("prompt_tokens", 0)) + int(emotion_usage.get("prompt_tokens", 0)) + int(gender_usage.get("prompt_tokens", 0)),
        "completion_tokens": int(intent_usage.get("completion_tokens", 0)) + int(emotion_usage.get("completion_tokens", 0)) + int(gender_usage.get("completion_tokens", 0)),
        "total_tokens": int(intent_usage.get("total_tokens", 0)) + int(emotion_usage.get("total_tokens", 0)) + int(gender_usage.get("total_tokens", 0)),
    }
    return out, latency_ms, usage_dict


def agree(a: Dict[str, Any], b: Dict[str, Any], key: str) -> int:
    va, vb = (a or {}).get(key), (b or {}).get(key)
    return int((va is not None) and (vb is not None) and (str(va).lower() == str(vb).lower()))


async def main():
    # Try env var; if missing, load from .env automatically
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        load_env_fallback()
        api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("OPENAI_API_KEY is required (set in env or .env)")

    client = openai.AsyncOpenAI(api_key=api_key)

    # Optional warm-up to avoid cold-start skew
    await asyncio.sleep(0.05)

    single_latencies = []
    triple_latencies = []
    intent_agree = []
    emotion_agree = []
    gender_agree = []
    # Track token usage per approach for averaging
    single_usage_list = []
    triple_usage_list = []

    # Single utterance benchmark
    utt = TEST_UTTERANCE
    clean = simple_clean_text(utt)

    single_out, single_ms, single_usage = await single_call_combined(client, clean)
    triple_out, triple_ms, triple_usage = await triple_call_parallel(client, clean)

    single_latencies.append(single_ms)
    triple_latencies.append(triple_ms)
    intent_agree.append(agree(single_out, triple_out, "intent"))
    emotion_agree.append(agree(single_out, triple_out, "emotion"))
    gender_agree.append(agree(single_out, triple_out, "gender"))

    single_usage_list.append(single_usage)
    triple_usage_list.append(triple_usage)

    single_prompt_toks = single_usage.get("prompt_tokens", 0)
    single_completion_toks = single_usage.get("completion_tokens", 0)
    single_total_toks = single_usage.get("total_tokens", 0)

    triple_prompt_toks = triple_usage.get("prompt_tokens", 0)
    triple_completion_toks = triple_usage.get("completion_tokens", 0)
    triple_total_toks = triple_usage.get("total_tokens", 0)

    print(f"\nPrompt used in both test: {utt}")
    print(f"  Single-call latency:      {single_ms:.1f} ms | tokens: prompt={single_prompt_toks}, completion={single_completion_toks}, total={single_total_toks}")
    print(f"  3-call parallel latency:  {triple_ms:.1f} ms | tokens: prompt={triple_prompt_toks}, completion={triple_completion_toks}, total={triple_total_toks}")
    print(f"  Single-call out: {single_out}")
    print(f"  3-call out:     {triple_out}")

    def avg(x):
        return mean(x) if x else 0.0

    print("\n==== Summary ====")
    print(f"Avg single-call latency:      {avg(single_latencies):.1f} ms")
    print(f"Avg 3-call parallel latency:  {avg(triple_latencies):.1f} ms")
    # Token averages across runs
    print(
        "Avg single-call tokens:       "
        f"prompt={avg([u.get('prompt_tokens', 0) for u in single_usage_list]):.1f}, "
        f"completion={avg([u.get('completion_tokens', 0) for u in single_usage_list]):.1f}, "
        f"total={avg([u.get('total_tokens', 0) for u in single_usage_list]):.1f}"
    )
    print(
        "Avg 3-call parallel tokens:   "
        f"prompt={avg([u.get('prompt_tokens', 0) for u in triple_usage_list]):.1f}, "
        f"completion={avg([u.get('completion_tokens', 0) for u in triple_usage_list]):.1f}, "
        f"total={avg([u.get('total_tokens', 0) for u in triple_usage_list]):.1f}"
    )
    print(f"Agreement — intent:  {sum(intent_agree)}/{len(intent_agree)}")
    print(f"Agreement — emotion: {sum(emotion_agree)}/{len(emotion_agree)}")
    print(f"Agreement — gender:  {sum(gender_agree)}/{len(gender_agree)}")


if __name__ == "__main__":
    asyncio.run(main())

