from pydantic import BaseModel, Field
from typing import Optional
from enum import Enum

class AudioTag(str, Enum):
    main = "main"
    filler = "filler"

class AudioSchema(BaseModel):
    """Schema for audio input/output validation"""
    audio_path: str = Field(..., description="Path to audio file")
    audio_data: Optional[bytes] = Field(None, description="Audio data in bytes")
    tag: AudioTag = Field(..., description="Tag for audio (main or filler)")
    id: int = Field(..., description="ID for audio")
