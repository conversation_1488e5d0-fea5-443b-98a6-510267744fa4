"""
Base agent classes for the Voice Agents Platform.

This module provides abstract base classes and common functionality for all agents
in the Voice Agents Platform, including integrated logging, error handling, and
standardized interfaces.
"""

import sys
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List, Union
import asyncio
import time
from datetime import datetime, timezone
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.memory.redis_context import RedisClient
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType
from core.logging.logger_config import get_module_logger
from core.memory.memory_manager import MemoryManager


class BaseAgent(ABC):
    """
    Abstract base class for all agents in the Voice Agents Platform.
    
    Provides common functionality including:
    - Integrated structured logging
    - Error handling and metrics collection
    - Standardized input/output interfaces
    - Session and state context management
    """
    
    def __init__(self, agent_name: str, session_id: str = None, state_id: str = None):
        """
        Initialize the base agent.
        
        Args:
            agent_name: Unique name for this agent
            session_id: Optional session ID for context
            state_id: Optional state ID for context
        """
        self.agent_name = agent_name
        self.session_id = session_id
        self.state_id = state_id
        self.logger = get_module_logger(agent_name, session_id, state_id)
        self.metrics = {}
        self.redis_client = RedisClient()

        self.logger.info(
            f"Initialized {agent_name} agent",
            action="initialize",
            layer="agent",
            agent_name=agent_name
        )
    
    def update_context(self, session_id: str = None, state_id: str = None):
        """Update the agent's session and state context."""
        if session_id:
            self.session_id = session_id
        if state_id:
            self.state_id = state_id
        self.logger.update_context(session_id, state_id)
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """
        Process input data and return a StateOutput.
        
        Args:
            input_data: Input data to process
            context: Optional context information
            
        Returns:
            StateOutput: Processed result with status, data, and metadata
        """
        pass
    
    def _log_process_start(self, input_data: Dict[str, Any], context: Dict[str, Any] = None):
        """Log the start of processing."""
        # Filter out non-serializable objects from context
        safe_context = self._make_serializable(context) if context else None
        safe_input = self._make_serializable(input_data)

        self.logger.info(
            f"Starting {self.agent_name} processing",
            action="process_start",
            input_data={"input": safe_input, "context": safe_context},
            layer="agent",
            agent_name=self.agent_name
        )
    
    def _log_process_end(self, result: StateOutput, duration_ms: float):
        """Log the end of processing."""
        self.logger.info(
            f"Completed {self.agent_name} processing",
            action="process_end",
            output_data={
                "status": result.status.value if hasattr(result.status, 'value') else str(result.status),
                "message": result.message
            },
            metrics={"duration_ms": duration_ms},
            layer="agent",
            agent_name=self.agent_name
        )
    
    def _log_error(self, error: Exception, input_data: Dict[str, Any] = None):
        """Log an error during processing."""
        safe_input = self._make_serializable(input_data) if input_data else None

        self.logger.error(
            f"Error in {self.agent_name} processing",
            action="process_error",
            input_data={"input": safe_input} if safe_input else None,
            reason=str(error),
            layer="agent",
            agent_name=self.agent_name
        )

    def _make_serializable(self, obj):
        """Convert objects to JSON-serializable format."""
        if obj is None:
            return None
        elif isinstance(obj, (str, int, float, bool)):
            return obj
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            # For objects with attributes, return their class name
            return f"<{obj.__class__.__name__} object>"
        else:
            # For other types, convert to string
            return str(obj)
    
    async def safe_process(self, input_data: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """
        Safely process input with error handling and logging.
        
        Args:
            input_data: Input data to process
            context: Optional context information
            
        Returns:
            StateOutput: Processed result or error state
        """
        start_time = time.perf_counter()
        
        try:
            self._log_process_start(input_data, context)
            if not self.validate_input(input_data):
                raise ValueError("Invalid input data")
            result = await self.process(input_data, context)
            duration_ms = (time.perf_counter() - start_time) * 1000
            self._log_process_end(result, duration_ms)
            return result
            
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            self._log_error(e, input_data)
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Error in {self.agent_name}: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={
                    "error": str(e),
                    "agent": self.agent_name,
                    "duration_ms": duration_ms
                }
            )
        
    @classmethod
    def input_schema(cls) -> Dict[str, Any]:
        """
        Returns the expected input schema for the agent.
        Subclasses may override this to provide validation or documentation.

        Returns:
            A JSON-schema-like dictionary or None if not defined.
        """
        return {}
    
    @classmethod
    def output_schema(cls) -> Dict[str, Any]:
        """
        Returns the expected output schema for the agent.
        Subclasses may override this.

        Returns:
            A JSON-schema-like dictionary or None if not defined.
        """
        return {}
    
    async def setup(self):
        """
        Optional method to initialize resources.
        Override in subclasses if setup is required.
        """
        pass

    async def teardown(self):
        """
        Optional method to release resources.
        Override in subclasses if teardown is needed.
        """
        pass

    @property
    def agent_type(self) -> str:
        """
        Returns the type or category of the agent.
        Override in subclasses to identify agent purpose (e.g., 'stt', 'summarizer').
        """
        return self.__class__.__name__

    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        Optional input validation hook.
        Subclasses may override this to enforce schema or raise errors.
        """
        return True

    def is_ready(self) -> bool:
        """
        Indicates if the agent is ready to process.
        Subclasses can override with custom readiness checks.
        """
        return True
    
    @property
    def a2a_channel(self) -> str:
        """
        The Redis pub/sub channel this agent listens to.
        """
        return f"agent_channel::{self.agent_name}"
    
    async def handle_message(self, raw_data: str):
        """
        Handle an incoming A2A message (as JSON string) from Redis.
        Uses A2AMessage schema and responds with a StateOutput.
        """
        try:
            message = A2AMessage.from_json(raw_data)

            input_data = message.payload
            context = {"session_id": message.session_id}
            result = await self.safe_process(input_data, context)

            # Build response A2AMessage
            response = A2AMessage(
                session_id=message.session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=self.agent_name,
                target_agent=message.source_agent,
                payload=result.model_dump(),
                context_keys_updated=None,
            )

            await self.redis_client.publish(f"agent_channel::{message.source_agent}", response.to_json())

        except Exception as e:
            self._log_error(e)
        
    async def start_a2a_loop(self):
        """
        Subscribe to this agent's A2A channel and process incoming messages.
        """
        await self.redis_client.subscribe(self.a2a_channel, self.handle_message)    
    
    async def save_context(self, key: str, value: dict, ex: int = 3600):
        """Save context or state to Redis."""
        await self.redis_client.set(key, value, ex=ex)

    async def load_context(self, key: str):
        """Load context or state from Redis."""
        return await self.redis_client.get(key)

    async def publish_notification(self, channel: str, message: dict):
        """Publish a notification/event to a Redis channel."""
        await self.redis_client.publish(channel, message)

    async def save_conversation_turn(self, user_message: str, ai_message: str, intent: Optional[str] = None):
        """
        Save a conversation turn (user and AI message) to contextual memory in Redis using MemoryManager.
        """
        session_id = self.session_id
        if not session_id:
            self.logger.warning("No session_id set for save_conversation_turn.")
            return
        memory_manager = MemoryManager(session_id)
        await memory_manager.save_conversation_turn(user_message, ai_message, intent)

    async def handle_redis_fallback(self, shared_context, session_id):
        """
        Checks if the shared_context is a Redis fallback dict and, if so, notifies orchestrator and returns a StateOutput with fallback message/audio.
        Returns None if not a fallback.
        """
        if isinstance(shared_context, dict) and "tts_response" in shared_context:
            notification = A2AMessage(
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent=getattr(self, 'agent_name', 'unknown_agent'),
                target_agent="Orchestrator",
                payload={"status": "error", "error_message": "Redis unavailable. Fallback triggered."},
                context_keys_updated=[]
            )
            await self.publish_notification("agent_notifications", notification.to_json())
            outputs = {"fallback_message": shared_context["tts_response"]}
            if "tts_audio_path" in shared_context:
                outputs["audio_path"] = shared_context["tts_audio_path"]
            return StateOutput(
                status=StatusType.ERROR,
                message="Redis unavailable. Fallback triggered.",
                code=StatusCode.SERVICE_UNAVAILABLE,
                outputs=outputs,
                meta={"agent": getattr(self, 'agent_name', 'unknown_agent')}
            )
        return None


class VoiceAgent(BaseAgent):
    """
    Base class for voice-related agents.
    
    Orchestrator agent that manages the voice interaction loop,
    queries other agents, and routes decisions.
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("voice_agent", session_id, state_id)
    
    @abstractmethod
    async def process_voice_interaction(self, audio_data: bytes, context: Dict[str, Any] = None) -> StateOutput:
        """Process a complete voice interaction."""
        pass


class AudioAgent(BaseAgent):
    """
    Base class for audio processing agents.
    
    Handles speech-to-text (STT), voice activity detection (VAD),
    audio cleaning, and text-to-speech (TTS).
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("audio_agent", session_id, state_id)
    
    async def speech_to_text(self, audio_data: bytes) -> StateOutput:
        """Convert speech to text."""
        pass
    
    
    async def text_to_speech(self, text: str, voice_config: Dict[str, Any] = None) -> StateOutput:
        """Convert text to speech."""
        pass
    
    
    async def detect_voice_activity(self, audio_data: bytes) -> StateOutput:
        """Detect voice activity in audio."""
        pass


class DisambiguatorAgent(BaseAgent):
    """
    Base class for language disambiguation agents.
    
    Fixes language issues, determines language and gender,
    normalizes ambiguous phrases, and supports multilingual conversion.
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("disambiguator", session_id, state_id)
    
    @abstractmethod
    async def detect_language(self, text: str) -> StateOutput:
        """Detect the language of input text."""
        pass
    
    @abstractmethod
    async def normalize_text(self, text: str, language: str = None) -> StateOutput:
        """Normalize and clean input text."""
        pass


class ResponseAgent(BaseAgent):
    """
    Base class for response generation agents.
    
    Parses intents, triggers RAG if needed, queries LLM,
    and returns appropriate responses.
    """
    
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("response_agent", session_id, state_id)
    
    # async def parse_intent(self, text: str, context: Dict[str, Any] = None) -> StateOutput:
    #     """Parse intent from input text."""
    #     pass
    
    async def generate_response(self, intent: str, entities: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """Generate response based on intent and entities."""
        pass
    
    async def query_knowledge_base(self, query: str, context: Dict[str, Any] = None) -> StateOutput:
        """Query knowledge base for relevant information."""
        pass


class MCPAgent(BaseAgent):
    """
    Base class for MCP (Modular Communication Protocol) agents.
    
    Provides standardized communication interface for agent-to-agent
    messaging and external service integration.
    """
    
    def __init__(self, agent_name: str, session_id: str = None, state_id: str = None):
        super().__init__(f"mcp_{agent_name}", session_id, state_id)
        self.mcp_capabilities = []
    
    @abstractmethod
    async def handle_mcp_message(self, message: Dict[str, Any]) -> StateOutput:
        """Handle incoming MCP message."""
        pass
    
    def register_capability(self, capability: str):
        """Register an MCP capability."""
        if capability not in self.mcp_capabilities:
            self.mcp_capabilities.append(capability)
            self.logger.info(
                f"Registered MCP capability: {capability}",
                action="register_capability",
                output_data={"capability": capability, "total_capabilities": len(self.mcp_capabilities)},
                layer="agent",
                agent_name=self.agent_name
            )
