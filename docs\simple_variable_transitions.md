# Simple Variable-Based Conditional Transitions

## Overview

This implementation provides a simple approach to support variable-based conditional transitions in workflow JSON files. The orchestrator now extracts variables from transition conditions and includes their values in the LLM evaluation context.

## Key Features

### 1. Enhanced Workflow JSON Support

Workflows can now include variable-based conditions in transitions:

```json
{
  "TransferFunds": {
    "transitions": [
      {
        "condition": "amount >= 60000",
        "target": "Goodbye"
      },
      {
        "condition": "balance < amount", 
        "target": "CheckBalance"
      },
      {
        "condition": "true",
        "target": "RepeatedUserQuery"
      }
    ]
  }
}
```

### 2. Simple Variable Extraction

The orchestrator uses a simple regex-based approach to extract variable names from conditions:

```python
def extract_variables_from_condition(self, condition: str) -> List[str]:
    # Extract variables like: amount, balance, account_type, etc.
    # From conditions like: "amount >= 60000 and balance > 1000"
```

### 3. Variable Context Building

Variables are retrieved from memory using multiple key formats:

1. `{session_id}_{workflow_state}_{variable}` (most specific)
2. `{workflow_state}_{variable}` 
3. `{variable}` (simple key)
4. `contextual_{variable}` (with prefix)

### 4. Enhanced LLM Evaluation

The LLM prompt now includes variable context:

```
Context:
- User Query: {user_query}
- Detected Intent: {intent}
- Variable Context: {variable_context}
- Allowed Transitions: {allowed_transitions}
```

## Implementation Details

### Core Methods Added to OrchestratorV3

1. **`extract_variables_from_condition(condition: str) -> List[str]`**
   - Simple regex-based variable extraction
   - Filters out Python keywords and operators

2. **`get_variable_context_for_transitions(allowed_transitions, workflow_state) -> Dict[str, Any]`**
   - Extracts variables from all transition conditions
   - Retrieves values from memory using multiple key formats
   - Returns context dictionary for LLM evaluation

### Variable Resolution Strategy

Variables are resolved in this priority order:
1. Session + workflow scoped: `session123_TransferFunds_amount`
2. Workflow scoped: `TransferFunds_amount`
3. Simple key: `amount`
4. Contextual prefix: `contextual_amount`

## Usage Examples

### Example 1: Banking Transfer Logic

```json
{
  "TransferFunds": {
    "transitions": [
      {
        "condition": "amount >= 60000",
        "target": "Goodbye"
      },
      {
        "condition": "balance < amount",
        "target": "CheckBalance" 
      },
      {
        "condition": "true",
        "target": "RepeatedUserQuery"
      }
    ]
  }
}
```

**Memory Setup:**
```python
await memory_manager.set("contextual", f"{session_id}_TransferFunds_amount", 75000)
await memory_manager.set("contextual", f"{session_id}_TransferFunds_balance", 100000)
```

**Expected Behavior:**
- If amount >= 60000: Transition to "Goodbye"
- If balance < amount: Transition to "CheckBalance"
- Otherwise: Transition to "RepeatedUserQuery"

### Example 2: Account Type Routing

```json
{
  "CustomerService": {
    "transitions": [
      {
        "condition": "account_type == 'premium'",
        "target": "PremiumService"
      },
      {
        "condition": "true",
        "target": "StandardService"
      }
    ]
  }
}
```

## Testing

Run the test example:

```bash
python examples/test_variable_transitions.py
```

This will test:
- Variable extraction from conditions
- Variable context building from memory
- Integration with workflow transitions

## Benefits of This Approach

1. **Simplicity**: Minimal code changes, leverages existing LLM evaluation
2. **Backward Compatibility**: Existing workflows continue to work unchanged
3. **Flexibility**: LLM can handle complex logic and edge cases
4. **Maintainability**: Easy to understand and modify
5. **Robustness**: Graceful fallback when variables are missing

## Limitations

1. **LLM Dependency**: Still relies on LLM for final decision making
2. **No Direct Condition Evaluation**: Variables are provided as context, not directly evaluated
3. **Performance**: LLM call required for each transition decision

## Migration Guide

### For Existing Workflows

1. Add variable-based conditions to transitions where needed
2. Ensure agents set variables in memory using consistent key formats
3. Test with various scenarios to ensure LLM makes correct decisions

### For New Workflows

1. Design transitions with variable conditions first
2. Use clear, descriptive variable names
3. Always include a fallback condition (`"true"`)
4. Set variables in memory before transition evaluation

## Best Practices

1. **Variable Naming**: Use clear, descriptive names (e.g., `transfer_amount`, `account_balance`)
2. **Key Formats**: Use session + workflow scoped keys for isolation
3. **Condition Order**: Place more specific conditions before general ones
4. **Fallback**: Always include a `"true"` condition as the last option
5. **Testing**: Test with various variable values and edge cases
