import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = str(Path(__file__).parent.parent.parent)
sys.path.insert(0, project_root)

# Load environment variables BEFORE importing agents
from dotenv import load_dotenv
load_dotenv()

from agents.processing.preprocessing_agent import PreprocessingAgent
import time

async def test_individual_functions():
    """Test individual preprocessing functions"""
    print("=== Testing Individual Functions ===")
    agent = PreprocessingAgent(session_id="test_session", state_id="test_state")

    test_text = "I'm frustrated! Please transfer $500 to my account immediately."

    # Test clean_text
    print(f"Original text: '{test_text}'")
    clean_result = await agent.clean_text(test_text)
    print(f"Clean text: '{clean_result}'")

    # Test classify_intent
    print("\nTesting intent classification...")
    intent_result = await agent.classify_intent(clean_result)
    print(f"Intent result: {intent_result}")

    # Test detect_emotion
    print("\nTesting emotion detection...")
    emotion_result = await agent.detect_emotion(clean_result)
    print(f"Emotion result: {emotion_result}")

    # Test detect_gender
    print("\nTesting gender detection...")
    gender_result = await agent.detect_gender(clean_result)
    print(f"Gender result: {gender_result}")

async def test_parallel_execution():
    """Test the parallel execution in the main process method"""
    print("\n=== Testing Parallel Execution ===")
    session_id = "test_parallel_session"
    redis_key = session_id

    agent = PreprocessingAgent(session_id=session_id, state_id="test_state")
    test_transcript = "I'm so excited! Can you please check my account balance?"

    # Save transcript to Redis
    await agent.save_context(redis_key, {"transcript": test_transcript})

    # Measure processing time
    start_time = time.perf_counter()
    result = await agent.process({})
    end_time = time.perf_counter()

    processing_time = (end_time - start_time) * 1000
    print(f"Processing time: {processing_time:.2f} ms")
    print(f"Result status: {result.status}")
    print(f"Result outputs: {result.outputs}")

    # Check context
    context = await agent.load_context(redis_key)
    print(f"Context after processing: {context}")

async def test_parallel_with_disambiguation():
    """Test parallel execution WITH disambiguation enabled"""
    print("\n=== Testing Parallel + Disambiguation ===")
    session_id = "test_disambig_session"
    redis_key = session_id

    agent = PreprocessingAgent(session_id=session_id, state_id="test_state")

    # Mock the load_config method to enable disambiguation
    original_load_config = agent.load_config
    def mock_load_config():
        config = original_load_config()
        config["enable_disambiguation"] = True
        return config
    agent.load_config = mock_load_config

    # Use text that needs disambiguation (transcription errors)
    test_transcript = "book a chip to paris and check my balance please"

    await agent.save_context(redis_key, {"transcript": test_transcript})

    start_time = time.perf_counter()
    result = await agent.process({})
    end_time = time.perf_counter()

    processing_time = (end_time - start_time) * 1000
    print(f"Processing time with disambiguation: {processing_time:.2f} ms")
    print(f"Original transcript: '{test_transcript}'")
    print(f"Clean text result: '{result.outputs.get('clean_text')}'")
    print(f"Intent: {result.outputs.get('intent')}")
    print(f"Emotion: {result.outputs.get('emotion')}")
    print(f"Result status: {result.status}")

    context = await agent.load_context(redis_key)
    print(f"Context clean_text: '{context.get('clean_text')}'")

    # Restore original method
    agent.load_config = original_load_config

async def test_config_flags():
    """Test with different config flags"""
    print("\n=== Testing Config Flags ===")
    session_id = "test_config_session"
    redis_key = session_id

    agent = PreprocessingAgent(session_id=session_id, state_id="test_state")
    test_transcript = "Can you show me my current account balance please?"

    await agent.save_context(redis_key, {"transcript": test_transcript})

    # Test with default config (should include emotion and gender)
    result = await agent.process({})
    print("With default config:")
    print(f"  Has emotion: {'emotion' in result.outputs}")
    print(f"  Has gender: {'gender' in result.outputs}")
    print(f"  Intent: {result.outputs.get('intent')}")
    print(f"  Emotion: {result.outputs.get('emotion')}")
    print(f"  Gender: {result.outputs.get('gender')}")

async def main():
    """Run all tests"""
    try:
        await test_individual_functions()
        await test_parallel_execution()
        await test_parallel_with_disambiguation()
        await test_config_flags()
        print("\n=== All Tests Completed ===")
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())