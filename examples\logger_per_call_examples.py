#!/usr/bin/env python3
"""
Examples of how to use the per-log-call console output control.

This script demonstrates how to control console output for individual log calls
while maintaining comprehensive file logging.
"""

import sys
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger, LoggerConfig

def example_1_default_behavior():
    """Example 1: Default behavior - only warnings and errors in console"""
    print("\n=== Example 1: Default Behavior ===")
    print("Only warnings and errors will show in console by default")
    
    # Set up logger with default settings
    LoggerConfig.setup_for_development()
    logger = get_module_logger("example_module", session_id="test_session")
    
    # These won't show in console (below warning level)
    logger.debug("This debug message won't show in console")
    logger.info("This info message won't show in console")
    
    # These will show in console
    logger.warning("⚠️ This warning will show in console")
    logger.error("❌ This error will show in console")

def example_2_force_console_output():
    """Example 2: Force specific logs to show in console"""
    print("\n=== Example 2: Force Console Output ===")
    print("Using console_output='all' to force logs to show in console")
    
    logger = get_module_logger("force_module", session_id="force_session")
    
    # Force debug message to show in console
    logger.debug("🔍 This debug message will show in console", console_output="all")
    
    # Force info message to show in console
    logger.info("ℹ️ This info message will show in console", console_output="all")
    
    # Force warning message to show in console
    logger.warning("⚠️ This warning will show in console", console_output="all")
    
    # Force error message to show in console
    logger.error("❌ This error will show in console", console_output="all")

def example_3_selective_console_output():
    """Example 3: Selective console output for different log levels"""
    print("\n=== Example 3: Selective Console Output ===")
    print("Using different console_output levels for different messages")
    
    logger = get_module_logger("selective_module", session_id="selective_session")
    
    # Only show errors in console for this debug message
    logger.debug("🔍 Debug message - only errors will show in console", console_output="error")
    
    # Only show warnings and above in console for this info message
    logger.info("ℹ️ Info message - only warnings+ will show in console", console_output="warn")
    
    # Show all levels in console for this warning message
    logger.warning("⚠️ Warning message - all levels will show in console", console_output="all")
    
    # Show only errors in console for this error message
    logger.error("❌ Error message - only errors will show in console", console_output="error")

def example_4_suppress_console_output():
    """Example 4: Suppress console output for specific logs"""
    print("\n=== Example 4: Suppress Console Output ===")
    print("Using console_output='false' to suppress console output")
    
    logger = get_module_logger("suppress_module", session_id="suppress_session")
    
    # This warning won't show in console even though it's a warning
    logger.warning("⚠️ This warning won't show in console", console_output="false")
    
    # This error won't show in console even though it's an error
    logger.error("❌ This error won't show in console", console_output="false")
    
    # But this error will show in console (default behavior)
    logger.error("❌ This error will show in console")

def example_5_mixed_usage():
    """Example 5: Mixed usage in a typical workflow"""
    print("\n=== Example 5: Mixed Usage in Workflow ===")
    print("Demonstrating typical usage patterns")
    
    logger = get_module_logger("workflow_module", session_id="workflow_session")
    
    # Normal workflow logs (won't show in console by default)
    logger.info("Starting user authentication process")
    logger.debug("Validating user credentials", console_output="false")
    logger.info("User credentials validated successfully")
    
    # Important step - force to show in console
    logger.info("🔐 User authenticated successfully", console_output="all")
    
    # Error handling - will show in console by default
    logger.error("❌ Authentication failed for user 'john_doe'")
    
    # Debug info - suppress console output
    logger.debug("Detailed error stack trace", console_output="false")
    
    # Recovery step - force to show in console
    logger.info("🔄 Attempting authentication retry", console_output="all")

def example_6_structured_logging_with_console_control():
    """Example 6: Structured logging with console output control"""
    print("\n=== Example 6: Structured Logging with Console Control ===")
    print("Using structured logging with console output control")
    
    logger = get_module_logger("structured_module", session_id="structured_session")
    
    # Structured log with forced console output
    logger.info(
        "Processing user request",
        action="process_request",
        input_data={"user_id": "123", "request_type": "balance_check"},
        layer="request_processing",
        console_output="all"
    )
    
    # Structured log with suppressed console output
    logger.debug(
        "Detailed request processing steps",
        action="process_request",
        input_data={"steps": ["validate", "authorize", "process"]},
        layer="request_processing",
        console_output="false"
    )
    
    # Structured error with forced console output
    logger.error(
        "Request processing failed",
        action="process_request",
        reason="Invalid user ID format",
        layer="request_processing",
        console_output="all"
    )

def example_7_environment_variables():
    """Example 7: Using environment variables with per-call control"""
    print("\n=== Example 7: Environment Variables + Per-Call Control ===")
    print("Environment variables set base behavior, per-call overrides")
    
    # This would use environment variables if set
    # export VOICE_AGENT_CONSOLE_LOG_LEVEL=error
    # export VOICE_AGENT_CONSOLE_OUTPUT=true
    
    logger = get_module_logger("env_module", session_id="env_session")
    
    # This respects environment variable settings
    logger.info("This message respects environment variable settings")
    
    # This overrides environment variable settings
    logger.info("This message overrides environment settings", console_output="all")

if __name__ == "__main__":
    print("🎯 Per-Call Console Output Control Examples")
    print("=" * 60)
    print("This demonstrates how to control console output for individual log calls")
    print("while maintaining comprehensive file logging.")
    print()
    
    # Run examples
    example_1_default_behavior()
    example_2_force_console_output()
    example_3_selective_console_output()
    example_4_suppress_console_output()
    example_5_mixed_usage()
    example_6_structured_logging_with_console_control()
    example_7_environment_variables()
    
    print("\n✅ All examples completed!")
    print("Check the log files in the 'logs' directory for all messages.")
    print()
    print("📋 Console Output Options:")
    print("  - console_output='debug'  : Show debug and above")
    print("  - console_output='info'   : Show info and above")
    print("  - console_output='warn'   : Show warnings and above (default)")
    print("  - console_output='error'  : Show errors only")
    print("  - console_output='all'    : Show all log levels")
    print("  - console_output='false'  : Suppress console output") 