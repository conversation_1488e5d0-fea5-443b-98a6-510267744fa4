import json
import sys
import time
from pathlib import Path
from typing import Optional
from pydantic import ValidationError
import aiofiles
import asyncio

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from schemas.workflow_schema import WorkflowWrapper
from core.logging.exceptions import SchemaError, WorkflowError, StateTransitionError
from core.logging.logger_config import get_module_logger

logger = get_module_logger("workflow_schema_loader")

class WorkflowSchemaLoader:
    @staticmethod
    async def load(file_path: str) -> Optional[WorkflowWrapper]:

        """
        Loads a workflow schema JSON file and parses it into a WorkflowWrapper object.

        Args:
            file_path (str): Path to the workflow JSON file.

        Returns:
            WorkflowWrapper: Parsed workflow object, or None if there's an error.

        Raises:
            SchemaError: If the JSON schema is invalid
            WorkflowError: If there are logical errors in the workflow definition
        """
        start_time = time.perf_counter()

        logger.debug(
            "Loading workflow file",
            action="load_workflow",
            input_data={"file_path": file_path},
            layer="schema_loading"
        )
        try:
            async with aiofiles.open(file_path, 'r') as f:
                data = json.loads(await f.read())
                # Use model_validate instead of constructor for validation
                workflow = WorkflowWrapper.model_validate(data)
                await WorkflowSchemaLoader.validate_workflow(workflow)

                duration_ms = (time.perf_counter() - start_time) * 1000
                logger.info(
                    "Successfully loaded workflow schema",
                    action="load_workflow",
                    output_data={"file": file_path, "workflow_id": workflow.workflow.id},
                    layer="schema_loading",
                    metrics={"duration_ms": duration_ms}
                )
                return workflow
        except FileNotFoundError:
            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.error(
                "Workflow file not found",
                action="load_workflow",
                input_data={"file": file_path},
                reason="File not found",
                layer="schema_loading",
                metrics={"duration_ms": duration_ms}
            )
            raise SchemaError(f"Workflow schema file not found: {file_path}")
        except json.JSONDecodeError as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.error(
                "Invalid JSON format in workflow file",
                action="load_workflow",
                input_data={"file": file_path},
                reason=str(e),
                layer="schema_loading",
                metrics={"duration_ms": duration_ms}
            )
            raise SchemaError(f"Invalid JSON format in workflow schema: {e}")
        except ValidationError as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.error(
                "Workflow schema validation failed",
                action="load_workflow",
                input_data={"file": file_path},
                reason=str(e),
                layer="schema_loading",
                metrics={"duration_ms": duration_ms}
            )
            raise SchemaError(f"Workflow schema validation failed: {e}")
    
    @staticmethod
    async def validate_workflow(workflow: WorkflowWrapper) -> None:
        """
        Validates the workflow object to ensure it meets all requirements.

        Args:
            workflow (WorkflowWrapper): The workflow object to validate.

        Raises:
            ValidationError: If the workflow does not meet validation requirements.
            ValueError: If there are logical errors in the workflow structure.
        """
        start_time = time.perf_counter()

        try:
            # Track state transitions to detect cycles
            state_transitions = {}
            
            expected_outputs = set()

            # Validate states and transitions
            for state_id, state in workflow.workflow.states.items():
                # Check for duplicate output keys across pipeline steps
                defined_outputs = {}
                for output in state.expected_output:
                    if output in defined_outputs:
                        raise ValueError(f"Duplicate output key '{output}' found in state '{state_id}'.")
                    defined_outputs[output] = state_id
                    expected_outputs.add(output)
                
                # Track state transitions for cycle detection
                if hasattr(state, 'transitions'):
                    state_transitions[state_id] = [t.target for t in state.transitions]
            
            # Check if all expected inputs are defined in the expected outputs
            for state_id, state in workflow.workflow.states.items():
                for input_key in state.expected_input:
                    if input_key not in expected_outputs:
                        raise ValueError(f"Expected input '{input_key}' in state '{state_id}' is not defined in any state's expected output.")

            # Detect cycles in state transitions
            # await WorkflowSchemaLoader._detect_cycles(state_transitions)

            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.debug(
                "Workflow validation completed successfully",
                action="validate_workflow",
                layer="schema_validation",
                metrics={"duration_ms": duration_ms}
            )
            
        except ValidationError as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.error(
                "Workflow validation failed - ValidationError",
                action="validate_workflow",
                reason=str(e),
                layer="schema_validation",
                metrics={"duration_ms": duration_ms}
            )
            raise e
        except ValueError as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.error(
                "Workflow validation failed - ValueError",
                action="validate_workflow",
                reason=str(e),
                layer="schema_validation",
                metrics={"duration_ms": duration_ms}
            )
            raise ValueError(f"Workflow validation failed: {str(e)}")

    @staticmethod
    async def _detect_cycles(transitions):
        """
        Detects cycles in state transitions using DFS.
        
        Args:
            transitions: Dictionary mapping state IDs to lists of target state IDs.
            
        Raises:
            StateTransitionError: If a cycle is detected in the state transitions.
        """
        visited = set()
        path = set()
        
        async def dfs(state):
            if state in path:
                cycle_path = " -> ".join(list(path) + [state])
                logger.error(
                    "Cycle detected in state transitions",
                    action="validate_workflow",
                    output_data={"cycle": cycle_path},
                    reason="Circular dependency in state transitions",
                    layer="schema_validation"
                )
                raise StateTransitionError(f"Cycle detected in state transitions: {cycle_path}")
            
            if state in visited or state not in transitions:
                return
            
            visited.add(state)
            path.add(state)
            
            for target in transitions[state]:
                await dfs(target)
            
            path.remove(state)
        
        for state in transitions:
            if state not in visited:
                await dfs(state)