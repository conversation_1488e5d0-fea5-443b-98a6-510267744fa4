import asyncio
import sys
import os
from pathlib import Path

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.session_manager.session_manager_v2 import SessionManagerV2

async def test_variable_extraction():
    """Test the simple variable extraction method"""
    print("=== Testing Variable Extraction ===")
    
    # Import the orchestrator to test the method
    from agents.orchestrator_agent_v3 import OrchestratorV3
    
    # Create a dummy orchestrator instance just to test the method
    class DummyOrchestrator:
        def extract_variables_from_condition(self, condition: str):
            import re
            if condition in ["true", "false"]:
                return []
            
            variable_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
            matches = re.findall(variable_pattern, condition)
            
            excluded = {
                'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None',
                'if', 'else', 'elif', 'for', 'while', 'def', 'class'
            }
            
            variables = [var for var in matches if var not in excluded]
            return variables
    
    dummy = DummyOrchestrator()
    
    test_conditions = [
        "amount >= 60000",
        "balance < amount", 
        "account_type == 'premium'",
        "amount >= 60000 and balance > 1000",
        "intent == 'transfer' and amount < balance",
        "true",
        "false"
    ]
    
    for condition in test_conditions:
        variables = dummy.extract_variables_from_condition(condition)
        print(f"Condition: '{condition}' -> Variables: {variables}")
    
    print()

async def test_workflow_integration():
    """Test integration with the enhanced workflow"""
    print("=== Testing Workflow Integration ===")
    
    try:
        # Initialize session manager
        session_manager = SessionManagerV2()
        await session_manager.initialize()
        
        # Create session with banking workflow
        session_id = await session_manager.create_session("banking_workflow_v2")
        print(f"Created session: {session_id}")
        
        # Get memory manager and set test variables
        memory_manager = session_manager.active_sessions[session_id]["memory_manager"]
        
        # Set variables that would be used in TransferFunds state transitions
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_amount", 75000)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_balance", 100000)
        await memory_manager.set("contextual", "intent", "fund_transfer")
        
        print("Set test variables:")
        print(f"  amount: 75000")
        print(f"  balance: 100000") 
        print(f"  intent: fund_transfer")
        
        # Get orchestrator
        orchestrator = await session_manager.initialize_orchestrator(session_id)
        
        # Test variable context extraction
        allowed_transitions = ["Goodbye", "CheckBalance", "RepeatedUserQuery"]
        variable_context = await orchestrator.get_variable_context_for_transitions(
            allowed_transitions, "TransferFunds"
        )
        
        print(f"\nExtracted variable context: {variable_context}")
        
        # Test with different scenarios
        print("\n=== Testing Different Scenarios ===")
        
        # Scenario 1: High value transfer (amount >= 60000)
        print("\nScenario 1: High value transfer (amount=75000 >= 60000)")
        print("Expected: Should transition to 'Goodbye' based on condition 'amount >= 60000'")
        
        # Scenario 2: Insufficient funds (balance < amount)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_amount", 150000)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_balance", 100000)
        
        variable_context_2 = await orchestrator.get_variable_context_for_transitions(
            allowed_transitions, "TransferFunds"
        )
        print(f"\nScenario 2: Insufficient funds (balance=100000 < amount=150000)")
        print(f"Variable context: {variable_context_2}")
        print("Expected: Should transition to 'CheckBalance' based on condition 'balance < amount'")
        
        # Scenario 3: Normal transfer
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_amount", 5000)
        await memory_manager.set("contextual", f"{session_id}_TransferFunds_balance", 100000)
        
        variable_context_3 = await orchestrator.get_variable_context_for_transitions(
            allowed_transitions, "TransferFunds"
        )
        print(f"\nScenario 3: Normal transfer (amount=5000, balance=100000)")
        print(f"Variable context: {variable_context_3}")
        print("Expected: Should transition to 'RepeatedUserQuery' based on condition 'true'")
        
        # Cleanup
        await session_manager.cleanup_session(session_id)
        await session_manager.shutdown()
        
        print("\n✓ All tests completed successfully!")
        
    except Exception as e:
        print(f"Error in workflow integration test: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run all tests"""
    print("Testing Variable-Based Conditional Transitions (Simplified)\n")
    
    await test_variable_extraction()
    await test_workflow_integration()
    
    print("\nSummary:")
    print("- Variable extraction: Simple regex-based approach")
    print("- Variable context: Retrieved from memory using multiple key formats")
    print("- LLM integration: Enhanced prompts include variable context")
    print("- Workflow transitions: Uses existing LLM evaluation with variable awareness")

if __name__ == "__main__":
    asyncio.run(main())
