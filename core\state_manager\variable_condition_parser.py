import re
import ast
import sys
from pathlib import Path
from typing import Set, Dict, Any, List
import time

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent)
sys.path.append(project_root)

from core.logging.logger_config import get_module_logger
from core.memory.memory_manager import MemoryManager

class VariableConditionParser:
    """
    Utility class for parsing variable-based conditions in workflow transitions.
    Extracts variable names from conditions and builds evaluation contexts.
    """
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.logger = get_module_logger("variable_condition_parser", session_id=session_id)
    
    def extract_variables_from_condition(self, condition: str) -> Set[str]:
        """
        Extract variable names from a condition string.
        
        Args:
            condition (str): The condition string (e.g., "amount >= 60000 and balance > 1000")
            
        Returns:
            Set[str]: Set of variable names found in the condition
        """
        start_time = time.perf_counter()
        
        try:
            self.logger.debug(
                "Extracting variables from condition",
                action="extract_variables",
                input_data={"condition": condition},
                layer="variable_condition_parser"
            )
            
            # Handle simple cases
            if condition in ["true", "false"]:
                return set()
            
            # Use AST to safely parse the condition and extract variable names
            variables = set()
            
            try:
                # Parse the condition as a Python expression
                tree = ast.parse(condition, mode='eval')
                
                # Walk the AST to find all Name nodes (variables)
                for node in ast.walk(tree):
                    if isinstance(node, ast.Name):
                        variables.add(node.id)
                    elif isinstance(node, ast.Attribute):
                        # Handle attribute access like "user.account_type"
                        if isinstance(node.value, ast.Name):
                            variables.add(node.value.id)
                            
            except SyntaxError:
                # Fallback to regex-based extraction for complex cases
                self.logger.warning(
                    "AST parsing failed, falling back to regex",
                    action="extract_variables",
                    input_data={"condition": condition},
                    layer="variable_condition_parser"
                )
                variables = self._extract_variables_regex(condition)
            
            # Filter out Python keywords and operators
            python_keywords = {
                'and', 'or', 'not', 'in', 'is', 'if', 'else', 'elif', 'for', 'while',
                'def', 'class', 'import', 'from', 'as', 'try', 'except', 'finally',
                'with', 'lambda', 'global', 'nonlocal', 'True', 'False', 'None'
            }
            
            variables = variables - python_keywords
            
            duration_ms = (time.perf_counter() - start_time) * 1000
            
            self.logger.info(
                "Variables extracted from condition",
                action="extract_variables",
                input_data={"condition": condition},
                output_data={"variables": list(variables)},
                layer="variable_condition_parser",
                metrics={"duration_ms": duration_ms}
            )
            
            return variables
            
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.error(
                "Error extracting variables from condition",
                action="extract_variables",
                input_data={"condition": condition},
                reason=str(e),
                layer="variable_condition_parser",
                metrics={"duration_ms": duration_ms}
            )
            return set()
    
    def _extract_variables_regex(self, condition: str) -> Set[str]:
        """
        Fallback regex-based variable extraction.
        
        Args:
            condition (str): The condition string
            
        Returns:
            Set[str]: Set of variable names found
        """
        # Pattern to match variable names (alphanumeric + underscore, starting with letter/underscore)
        variable_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\b'
        
        # Find all potential variable names
        matches = re.findall(variable_pattern, condition)
        
        # Filter out common operators and keywords that might be matched
        operators_and_keywords = {
            'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None',
            'eq', 'ne', 'lt', 'le', 'gt', 'ge'
        }
        
        variables = set(matches) - operators_and_keywords
        return variables
    
    async def build_evaluation_context(
        self, 
        variables: Set[str], 
        memory_manager: MemoryManager,
        workflow_state: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Build evaluation context by retrieving variable values from memory.
        
        Args:
            variables (Set[str]): Set of variable names to retrieve
            memory_manager (MemoryManager): Memory manager instance
            workflow_state (str): Current workflow state
            additional_context (Dict[str, Any]): Additional context variables
            
        Returns:
            Dict[str, Any]: Context dictionary for condition evaluation
        """
        start_time = time.perf_counter()
        
        try:
            self.logger.debug(
                "Building evaluation context",
                action="build_context",
                input_data={"variables": list(variables), "workflow_state": workflow_state},
                layer="variable_condition_parser"
            )
            
            context = {}
            
            # Add additional context if provided
            if additional_context:
                context.update(additional_context)
            
            # Retrieve variable values from memory
            for variable in variables:
                value = await self._get_variable_value(variable, memory_manager, workflow_state)
                if value is not None:
                    context[variable] = value
                    self.logger.debug(
                        f"Retrieved variable value",
                        action="build_context",
                        input_data={"variable": variable, "value": value},
                        layer="variable_condition_parser"
                    )
                else:
                    self.logger.warning(
                        f"Variable not found in memory",
                        action="build_context",
                        input_data={"variable": variable},
                        layer="variable_condition_parser"
                    )
            
            duration_ms = (time.perf_counter() - start_time) * 1000
            
            self.logger.info(
                "Evaluation context built",
                action="build_context",
                output_data={"context_keys": list(context.keys())},
                layer="variable_condition_parser",
                metrics={"duration_ms": duration_ms}
            )
            
            return context
            
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            self.logger.error(
                "Error building evaluation context",
                action="build_context",
                input_data={"variables": list(variables)},
                reason=str(e),
                layer="variable_condition_parser",
                metrics={"duration_ms": duration_ms}
            )
            return {}
    
    async def _get_variable_value(
        self, 
        variable: str, 
        memory_manager: MemoryManager, 
        workflow_state: str
    ) -> Any:
        """
        Retrieve a variable value from memory using multiple key formats.
        
        Args:
            variable (str): Variable name
            memory_manager (MemoryManager): Memory manager instance
            workflow_state (str): Current workflow state
            
        Returns:
            Any: Variable value or None if not found
        """
        # Try different key formats in order of preference
        key_formats = [
            f"{self.session_id}_{workflow_state}_{variable}",  # Session-workflow-scoped
            f"{workflow_state}_{variable}",                    # Workflow-scoped
            variable,                                          # Simple key
            f"contextual_{variable}",                          # Contextual prefix
        ]
        
        for key in key_formats:
            value = await memory_manager.get(key)
            if value is not None:
                return value
        
        return None
    
    def extract_variables_from_transitions(self, transitions: List[Dict[str, str]]) -> Set[str]:
        """
        Extract all variables from a list of transitions.
        
        Args:
            transitions (List[Dict[str, str]]): List of transition dictionaries
            
        Returns:
            Set[str]: Set of all variable names found in transitions
        """
        all_variables = set()
        
        for transition in transitions:
            condition = transition.get('condition', '')
            variables = self.extract_variables_from_condition(condition)
            all_variables.update(variables)
        
        return all_variables
